import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { BusinessPointsSummary } from '../../../services/types';

interface PointsSummaryCardsProps {
  pointsData: BusinessPointsSummary | null;
}

export function PointsSummaryCards({ pointsData }: PointsSummaryCardsProps) {
  return (
    <VStack space='lg'>
      {/* Active Points Card */}
      <Box className='bg-primary-500 rounded-2xl border-4 border-primary-700 shadow-lg p-6'>
        <VStack space='xs'>
          <Text size='lg' className='text-white font-medium'>
            Active points
          </Text>
          <Heading size='4xl' className='text-white font-bold'>
            {pointsData?.totalActive?.toLocaleString() || '0'}
          </Heading>
        </VStack>
      </Box>

      {/* Points Awarded and Redeemed Cards */}
      <HStack space='md'>
        {/* Points Awarded Card */}
        <Box className='flex-1 bg-secondary-500 rounded-2xl border-4 border-secondary-700 shadow-lg p-4'>
          <VStack space='xs'>
            <Text size='md' className='text-white font-medium'>
              Points awarded
            </Text>
            <Heading size='2xl' className='text-white font-bold'>
              {pointsData?.totalEarned?.toLocaleString() || '0'}
            </Heading>
          </VStack>
        </Box>

        {/* Points Redeemed Card */}
        <Box className='flex-1 bg-error-500 rounded-2xl border-4 border-error-700 shadow-lg p-4'>
          <VStack space='xs'>
            <Text size='md' className='text-white font-medium'>
              Points redeemed
            </Text>
            <Heading size='2xl' className='text-white font-bold'>
              {pointsData?.totalRedeemed?.toLocaleString() || '0'}
            </Heading>
          </VStack>
        </Box>
      </HStack>
    </VStack>
  );
}
