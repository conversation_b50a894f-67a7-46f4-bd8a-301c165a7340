import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface PointsErrorStateProps {
  error: string;
}

export function PointsErrorState({ error }: PointsErrorStateProps) {
  return (
    <VStack space='lg' className='items-center py-8'>
      <Text size='lg' className='text-error-500 font-semibold text-center'>
        Unable to load points data
      </Text>
      <Text size='md' className='text-typography-600 text-center'>
        {error}
      </Text>
      <Text size='sm' className='text-typography-500 text-center'>
        Please try again later or contact support if the problem persists.
      </Text>
    </VStack>
  );
}
