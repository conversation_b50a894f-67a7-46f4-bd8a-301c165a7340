import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import dayjs from 'dayjs';
import React from 'react';
import { ActivityIndicator } from 'react-native';

interface Transaction {
  id: string;
  amountSpent: number;
  pointsAwarded: number;
  pointsRedeemed: number;
  createdAt: string;
  redeemedRewards?: Array<{ title: string }>;
}

interface TransactionsListProps {
  transactions: Transaction[];
  loadingMore: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

export function TransactionsList({
  transactions,
  loadingMore,
  hasMore,
  onLoadMore,
}: TransactionsListProps) {
  if (transactions.length === 0) {
    return (
      <VStack space='lg' className='items-center py-8'>
        <FontAwesome name='history' size={48} color='#9CA3AF' />
        <Text
          size='md'
          className='text-typography-600 text-center'
        >
          No transactions yet
        </Text>
        <Text
          size='sm'
          className='text-typography-500 text-center'
        >
          Transactions will appear here once customers start making purchases
        </Text>
      </VStack>
    );
  }

  return (
    <VStack space='md'>
      {transactions.map((transaction, index) => (
        <Box
          key={transaction.id}
          className='bg-white rounded-2xl border-2 border-typography-300 shadow-sm p-4'
        >
          <VStack space='sm'>
            <HStack className='justify-between items-start'>
              <VStack space='xs' className='flex-1'>
                <Text
                  size='md'
                  className='text-typography-900 font-semibold'
                >
                  £{transaction.amountSpent.toFixed(2)}
                </Text>
                <Text size='sm' className='text-typography-600'>
                  {dayjs(transaction.createdAt).format('MMM D, YYYY h:mm A')}
                </Text>
              </VStack>
              <VStack space='xs' className='items-end'>
                <Text size='sm' className='text-secondary-600 font-medium'>
                  +{transaction.pointsAwarded} points
                </Text>
                {transaction.pointsRedeemed > 0 && (
                  <Text size='sm' className='text-error-600 font-medium'>
                    -{transaction.pointsRedeemed} points
                  </Text>
                )}
              </VStack>
            </HStack>
            {transaction.redeemedRewards && transaction.redeemedRewards.length > 0 && (
              <Text size='sm' className='text-typography-700'>
                <Text className='font-medium'>Rewards redeemed:</Text>{' '}
                {transaction.redeemedRewards.map(r => r.title).join(', ')}
              </Text>
            )}
          </VStack>
        </Box>
      ))}
      
      {/* Load More / Loading Indicator */}
      {hasMore && (
        <Box className='items-center py-4'>
          {loadingMore ? (
            <ActivityIndicator size='small' color='#0284C7' />
          ) : (
            <Text
              size='sm'
              className='text-primary-600 font-medium'
              onPress={onLoadMore}
            >
              Load more transactions
            </Text>
          )}
        </Box>
      )}
    </VStack>
  );
}
