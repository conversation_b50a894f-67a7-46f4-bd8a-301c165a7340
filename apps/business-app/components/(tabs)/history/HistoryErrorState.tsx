import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface HistoryErrorStateProps {
  error: string;
}

export function HistoryErrorState({ error }: HistoryErrorStateProps) {
  return (
    <VStack space='lg' className='items-center py-8'>
      <FontAwesome name='exclamation-triangle' size={48} color='#ef4444' />
      <Text size='lg' className='text-error-500 font-semibold text-center'>
        Unable to load history
      </Text>
      <Text size='md' className='text-typography-600 text-center'>
        {error}
      </Text>
    </VStack>
  );
}
