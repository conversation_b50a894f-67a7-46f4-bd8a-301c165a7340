import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import dayjs from 'dayjs';
import React from 'react';

interface Customer {
  customerId: string;
  totalSpent: number;
  totalPointsEarned: number;
  totalPointsRedeemed: number;
  activePoints: number;
  transactionCount: number;
  lastTransactionAt: string;
}

interface CustomersListProps {
  customers: Customer[];
}

export function CustomersList({ customers }: CustomersListProps) {
  if (customers.length === 0) {
    return (
      <VStack space='lg' className='items-center py-8'>
        <FontAwesome name='users' size={48} color='#9CA3AF' />
        <Text size='md' className='text-typography-600 text-center'>
          No customers yet
        </Text>
        <Text size='sm' className='text-typography-500 text-center'>
          Customer data will appear here once they start making purchases
        </Text>
      </VStack>
    );
  }

  return (
    <VStack space='md'>
      {customers.map((customer, index) => (
        <Box
          key={customer.customerId}
          className='bg-white rounded-2xl border-2 border-typography-300 shadow-sm p-4'
        >
          <VStack space='sm'>
            <HStack className='justify-between items-start'>
              <VStack space='xs' className='flex-1'>
                <Text size='md' className='text-typography-900 font-semibold'>
                  Customer #{index + 1}
                </Text>
                <Text size='sm' className='text-typography-600'>
                  {customer.transactionCount} transaction
                  {customer.transactionCount !== 1 ? 's' : ''}
                </Text>
                <Text size='sm' className='text-typography-600'>
                  Last visit:{' '}
                  {dayjs(customer.lastTransactionAt).format('MMM D, YYYY')}
                </Text>
              </VStack>
              <VStack space='xs' className='items-end'>
                <Text size='sm' className='text-typography-900 font-medium'>
                  £{customer.totalSpent.toFixed(2)} spent
                </Text>
                <Text size='sm' className='text-primary-600 font-medium'>
                  {customer.activePoints} active points
                </Text>
              </VStack>
            </HStack>
            <HStack className='justify-between'>
              <Text size='sm' className='text-secondary-600'>
                {customer.totalPointsEarned} points earned
              </Text>
              <Text size='sm' className='text-error-600'>
                {customer.totalPointsRedeemed} points redeemed
              </Text>
            </HStack>
          </VStack>
        </Box>
      ))}
    </VStack>
  );
}
