import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import React from 'react';

import { HistoryTab } from '../../../hooks/composite/useHistoryScreenData';

interface TabSelectorProps {
  activeTab: HistoryTab;
  onTabChange: (tab: HistoryTab) => void;
}

export function TabSelector({ activeTab, onTabChange }: TabSelectorProps) {
  return (
    <HStack space='sm' className='mb-6'>
      <Pressable className='flex-1' onPress={() => onTabChange('transactions')}>
        <Box
          className={`p-4 rounded-xl border-2 ${
            activeTab === 'transactions'
              ? 'bg-primary-500 border-primary-700'
              : 'bg-white border-typography-300'
          }`}
        >
          <Text
            size='md'
            className={`font-semibold text-center ${
              activeTab === 'transactions'
                ? 'text-white'
                : 'text-typography-900'
            }`}
          >
            Transactions
          </Text>
        </Box>
      </Pressable>
      <Pressable className='flex-1' onPress={() => onTabChange('customers')}>
        <Box
          className={`p-4 rounded-xl border-2 ${
            activeTab === 'customers'
              ? 'bg-primary-500 border-primary-700'
              : 'bg-white border-typography-300'
          }`}
        >
          <Text
            size='md'
            className={`font-semibold text-center ${
              activeTab === 'customers' ? 'text-white' : 'text-typography-900'
            }`}
          >
            Customers
          </Text>
        </Box>
      </Pressable>
    </HStack>
  );
}
