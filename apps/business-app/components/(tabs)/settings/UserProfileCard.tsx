import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface UserProfileCardProps {
  userEmail?: string;
}

export function UserProfileCard({ userEmail }: UserProfileCardProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <HStack space='md' className='items-center'>
        {/* Profile Icon */}
        <Box className='w-16 h-16 bg-primary-500 rounded-2xl items-center justify-center'>
          <FontAwesome name='user' size={24} color='white' />
        </Box>

        {/* User Info */}
        <VStack className='flex-1'>
          <Text size='lg' className='text-typography-900 font-semibold'>
            {userEmail || 'Not signed in'}
          </Text>
          <Text size='sm' className='text-typography-600'>
            Business Owner
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
}
