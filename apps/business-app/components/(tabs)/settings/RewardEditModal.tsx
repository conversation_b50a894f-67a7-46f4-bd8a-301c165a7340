import { Box } from '@indie-points/ui-box';
import { But<PERSON>, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Input, InputField } from '@indie-points/ui-input';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { BusinessReward } from '../../../services/types';

interface RewardEditModalProps {
  editingReward: BusinessReward | null;
  rewardTitle: string;
  rewardDescription: string;
  rewardPoints: string;
  saving: boolean;
  onCancel: () => void;
  onSave: () => void;
  onDelete?: () => void;
  onTitleChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
  onPointsChange: (value: string) => void;
}

export function RewardEditModal({
  editingReward,
  rewardTitle,
  rewardDescription,
  rewardPoints,
  saving,
  onCancel,
  onSave,
  onDelete,
  onTitleChange,
  onDescriptionChange,
  onPointsChange,
}: RewardEditModalProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <VStack space='lg'>
        <HStack className='justify-between items-center'>
          <Heading size='lg' className='text-typography-900 font-semibold'>
            {editingReward ? 'Edit reward' : 'Add new reward'}
          </Heading>
          {editingReward && onDelete && (
            <Button size='sm' action='negative' onPress={onDelete}>
              <ButtonText>Delete</ButtonText>
            </Button>
          )}
        </HStack>

        <VStack space='md'>
          <VStack space='sm'>
            <Text size='md' className='text-typography-900 font-medium'>
              Reward title *
            </Text>
            <Input>
              <InputField
                placeholder='e.g., £5 off'
                value={rewardTitle}
                onChangeText={onTitleChange}
              />
            </Input>
          </VStack>

          <VStack space='sm'>
            <Text size='md' className='text-typography-900 font-medium'>
              Description *
            </Text>
            <Input>
              <InputField
                placeholder='e.g., £5 off your next transaction'
                value={rewardDescription}
                onChangeText={onDescriptionChange}
              />
            </Input>
          </VStack>

          <VStack space='sm'>
            <Text size='md' className='text-typography-900 font-medium'>
              Points required *
            </Text>
            <Input>
              <InputField
                placeholder='e.g., 100'
                value={rewardPoints}
                onChangeText={onPointsChange}
                keyboardType='numeric'
              />
            </Input>
          </VStack>

          <HStack space='md'>
            <Button
              size='lg'
              action='secondary'
              className='flex-1'
              onPress={onCancel}
            >
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button
              size='lg'
              action='primary'
              className='flex-1'
              onPress={onSave}
              disabled={saving}
            >
              {saving ? (
                <Spinner size='small' color='white' />
              ) : (
                <ButtonText>Confirm</ButtonText>
              )}
            </Button>
          </HStack>
        </VStack>
      </VStack>
    </Box>
  );
}
