import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Input, InputField } from '@indie-points/ui-input';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { BusinessProfile } from '../../../services/types';

interface BusinessProfileCardProps {
  businessProfile: BusinessProfile | null;
  editing: boolean;
  businessName: string;
  businessType: string;
  saving: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onSave: () => void;
  onBusinessNameChange: (value: string) => void;
  onBusinessTypeChange: (value: string) => void;
}

export function BusinessProfileCard({
  businessProfile,
  editing,
  businessName,
  businessType,
  saving,
  onEdit,
  onCancel,
  onSave,
  onBusinessNameChange,
  onBusinessTypeChange,
}: BusinessProfileCardProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <VStack space='lg'>
        <HStack space='md' className='items-center justify-between'>
          <HStack space='md' className='items-center'>
            <FontAwesome name='building' size={24} color='#1f2937' />
            <Heading
              size='lg'
              className='text-typography-900 font-semibold'
            >
              Business profile
            </Heading>
          </HStack>
          {businessProfile && !editing && (
            <Button
              size='sm'
              action='secondary'
              onPress={onEdit}
            >
              <ButtonText>Edit</ButtonText>
            </Button>
          )}
        </HStack>

        {editing ? (
          // Edit mode
          <VStack space='md'>
            <VStack space='sm'>
              <Text
                size='md'
                className='text-typography-900 font-medium'
              >
                Business name *
              </Text>
              <Input>
                <InputField
                  placeholder='Enter your business name'
                  value={businessName}
                  onChangeText={onBusinessNameChange}
                />
              </Input>
            </VStack>

            <VStack space='sm'>
              <Text
                size='md'
                className='text-typography-900 font-medium'
              >
                Business type *
              </Text>
              <Input>
                <InputField
                  placeholder='e.g., Restaurant, Retail, Gym'
                  value={businessType}
                  onChangeText={onBusinessTypeChange}
                />
              </Input>
            </VStack>

            <HStack space='md'>
              <Button
                size='lg'
                action='secondary'
                className='flex-1'
                onPress={onCancel}
              >
                <ButtonText>Cancel</ButtonText>
              </Button>
              <Button
                size='lg'
                action='primary'
                className='flex-1'
                onPress={onSave}
                disabled={saving}
              >
                {saving ? (
                  <Spinner size='small' color='white' />
                ) : (
                  <ButtonText>Save</ButtonText>
                )}
              </Button>
            </HStack>
          </VStack>
        ) : businessProfile ? (
          // View mode
          <VStack space='md'>
            <VStack space='sm'>
              <Text
                size='sm'
                className='text-typography-600 font-medium'
              >
                Business name
              </Text>
              <Text size='md' className='text-typography-900'>
                {businessProfile.businessName}
              </Text>
            </VStack>

            <VStack space='sm'>
              <Text
                size='sm'
                className='text-typography-600 font-medium'
              >
                Business type
              </Text>
              <Text size='md' className='text-typography-900'>
                {businessProfile.businessType}
              </Text>
            </VStack>
          </VStack>
        ) : (
          // No profile exists
          <VStack space='md' className='items-center'>
            <FontAwesome
              name='building-o'
              size={48}
              color='#9CA3AF'
            />
            <Text
              size='md'
              className='text-typography-600 text-center'
            >
              No business profile found
            </Text>
            <Text
              size='sm'
              className='text-typography-500 text-center'
            >
              Create your business profile to start using the app
            </Text>
          </VStack>
        )}
      </VStack>
    </Box>
  );
}
