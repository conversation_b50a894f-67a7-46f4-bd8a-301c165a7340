import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { BusinessReward } from '../../../services/types';

interface RewardsManagementCardProps {
  rewards: BusinessReward[];
  loadingRewards: boolean;
  onAddReward: () => void;
  onEditReward: (reward: BusinessReward) => void;
}

export function RewardsManagementCard({
  rewards,
  loadingRewards,
  onAddReward,
  onEditReward,
}: RewardsManagementCardProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <VStack space='lg'>
        <HStack space='md' className='items-center justify-between'>
          <HStack space='md' className='items-center'>
            <FontAwesome name='gift' size={24} color='#1f2937' />
            <Heading size='lg' className='text-typography-900 font-semibold'>
              Rewards
            </Heading>
          </HStack>
        </HStack>

        {loadingRewards ? (
          <VStack space='md' className='items-center py-4'>
            <Spinner size='small' />
            <Text size='sm' className='text-typography-600'>
              Loading rewards...
            </Text>
          </VStack>
        ) : rewards.length === 0 ? (
          <VStack space='md' className='items-center py-4'>
            <Text size='sm' className='text-typography-500 text-center'>
              Add rewards to incentivise customer loyalty
            </Text>
          </VStack>
        ) : (
          <VStack space='md'>
            {rewards
              .slice()
              .sort((a, b) => b.pointsRequired - a.pointsRequired)
              .map(reward => (
                <Box
                  key={reward.id}
                  className='bg-background-50 border-2 border-background-300 rounded-xl p-4'
                >
                  <VStack space='sm'>
                    <HStack space='md' className='items-start justify-between'>
                      <VStack space='xs' className='flex-1'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          {reward.title}
                        </Text>
                        <Text size='sm' className='text-typography-600'>
                          {reward.description}
                        </Text>
                      </VStack>
                      <VStack space='xs' className='items-end'>
                        <Button
                          size='sm'
                          action='secondary'
                          className='mb-2 w-20'
                          onPress={() => onEditReward(reward)}
                        >
                          <ButtonText>Edit</ButtonText>
                        </Button>
                      </VStack>
                    </HStack>
                    <HStack space='md' className='items-center'>
                      <Text
                        size='sm'
                        className='text-typography-700 font-medium'
                      >
                        {reward.pointsRequired} points required
                      </Text>
                    </HStack>
                  </VStack>
                </Box>
              ))}
          </VStack>
        )}
        <Button
          size='md'
          action='primary'
          className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
          onPress={onAddReward}
        >
          <ButtonText className='text-white font-semibold text-lg'>
            Add reward
          </ButtonText>
        </Button>
      </VStack>
    </Box>
  );
}
