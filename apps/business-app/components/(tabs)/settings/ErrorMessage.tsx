import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import React from 'react';

interface ErrorMessageProps {
  error: string;
}

export function ErrorMessage({ error }: ErrorMessageProps) {
  return (
    <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
      <HStack space='md' className='items-center'>
        <FontAwesome name='exclamation-triangle' size={20} color='#EF4444' />
        <Text size='md' className='text-error-700 font-semibold flex-1'>
          {error}
        </Text>
      </HStack>
    </Box>
  );
}
