import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React, { useRef, useState } from 'react';
import QRCode from 'react-native-qrcode-svg';

import { BusinessProfile } from '../../../services/types';

interface BusinessQRCodeSectionProps {
  businessProfile: BusinessProfile;
  onPrint: () => void;
  isPrinting: boolean;
  onQRRef?: (ref: any) => void;
}

export function BusinessQRCodeSection({
  businessProfile,
  onPrint,
  isPrinting,
  onQRRef,
}: BusinessQRCodeSectionProps) {
  const [qrWidth, setQrWidth] = useState(0);
  const qrRef = useRef<any>(null);

  // Helper to generate business QR code payload
  const generateBusinessQrPayload = () => {
    const now = Date.now();
    return JSON.stringify({
      businessId: businessProfile.id,
      businessName: businessProfile.businessName,
      token: `${businessProfile.id}-${now}`, // Simple token generation
    });
  };

  // Pass ref to parent when it's available
  React.useEffect(() => {
    if (qrRef.current && onQRRef) {
      onQRRef(qrRef.current);
    }
  }, [qrRef.current, onQRRef]);

  return (
    <VStack space='lg' className='items-center'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        Your business QR code
      </Heading>
      <Text size='md' className='text-typography-600 text-center'>
        Display this code for customers to scan and earn points
      </Text>

      {/* QR Code */}
      <Box
        className='w-full max-w-md aspect-square bg-white rounded-2xl border-4 p-4 border-typography-900 items-center justify-center shadow-lg'
        onLayout={event => {
          const width = event.nativeEvent.layout.width;
          setQrWidth(width);
        }}
      >
        <VStack space='md' className='items-center w-full h-full'>
          {businessProfile && qrWidth > 0 ? (
            <QRCode
              value={generateBusinessQrPayload()}
              size={Math.max(qrWidth - 8 - 32, 0)}
              logo={require('../../../assets/images/icon.png')}
              logoSize={Math.max((qrWidth - 8 - 32) * 0.2, 32)}
              logoBackgroundColor='transparent'
              getRef={c => (qrRef.current = c)}
            />
          ) : (
            <FontAwesome name='qrcode' size={120} color='#000' />
          )}
        </VStack>
      </Box>

      {/* Print Button */}
      <Button
        size='lg'
        className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
        onPress={onPrint}
        disabled={isPrinting}
      >
        <HStack space='sm' className='items-center'>
          <FontAwesome name='print' size={16} color='white' />
          <ButtonText className='text-white font-semibold'>
            {isPrinting ? 'Printing...' : 'Print'}
          </ButtonText>
        </HStack>
      </Button>
    </VStack>
  );
}
