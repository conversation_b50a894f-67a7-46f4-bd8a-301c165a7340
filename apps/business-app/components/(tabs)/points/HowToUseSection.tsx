import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

export function HowToUseSection() {
  return (
    <VStack space='lg'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        How customers earn points
      </Heading>

      {/* Step 1 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            1
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Display your QR code
          </Text>
          <Text size='sm' className='text-typography-600'>
            Show this QR code prominently in your business for customers to see
          </Text>
        </VStack>
      </HStack>

      {/* Step 2 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            2
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Customers scan your code
          </Text>
          <Text size='sm' className='text-typography-600'>
            Customers use the Indie Points app to scan your QR code when they
            visit
          </Text>
        </VStack>
      </HStack>

      {/* Step 3 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            3
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Points awarded automatically
          </Text>
          <Text size='sm' className='text-typography-600'>
            Customers earn 1 point for visiting, plus points for purchases you
            process
          </Text>
        </VStack>
      </HStack>
    </VStack>
  );
}
