import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import { CameraView } from 'expo-camera';
import React from 'react';

interface CameraScanSectionProps {
  hasPermission: boolean;
  onBarCodeScanned: (data: string) => void;
  onRequestPermission: () => void;
}

export function CameraScanSection({
  hasPermission,
  onBarCodeScanned,
  onRequestPermission,
}: CameraScanSectionProps) {
  if (!hasPermission) {
    return (
      <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
        <VStack space='lg' className='items-center'>
          <FontAwesome name='camera' size={48} color='#6B7280' />
          <Heading
            size='lg'
            className='text-typography-900 font-semibold text-center'
          >
            Camera permission required
          </Heading>
          <Text size='md' className='text-typography-600 text-center'>
            We need camera access to scan customer QR codes
          </Text>
          <Button
            size='lg'
            action='primary'
            className='w-full'
            onPress={onRequestPermission}
          >
            <ButtonText>Grant camera permission</ButtonText>
          </Button>
        </VStack>
      </Box>
    );
  }

  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg overflow-hidden'>
      <VStack space='lg'>
        <Box className='px-6 pt-6'>
          <Heading
            size='lg'
            className='text-typography-900 font-semibold text-center'
          >
            Scan customer QR code
          </Heading>
          <Text size='md' className='text-typography-600 text-center mt-2'>
            Ask the customer to show their QR code from the Indie Points app
          </Text>
        </Box>
        
        <Box className='aspect-square mx-6 mb-6 rounded-xl overflow-hidden'>
          <CameraView
            style={{ flex: 1 }}
            facing='back'
            onBarcodeScanned={({ data }) => onBarCodeScanned(data)}
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
          />
        </Box>
      </VStack>
    </Box>
  );
}
