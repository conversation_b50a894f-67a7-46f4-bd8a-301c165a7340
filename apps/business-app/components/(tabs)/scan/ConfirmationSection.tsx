import { Box } from '@indie-points/ui-box';
import { But<PERSON>, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { TransactionSummary } from '../../../hooks/composite/useScanScreenData';

interface ConfirmationSectionProps {
  transactionSummary: TransactionSummary;
  isProcessing: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export function ConfirmationSection({
  transactionSummary,
  isProcessing,
  onCancel,
  onConfirm,
}: ConfirmationSectionProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <VStack space='lg' className='items-center'>
        <Heading
          size='lg'
          className='text-typography-900 font-semibold text-center'
        >
          Confirm transaction
        </Heading>
        
        <Box className='w-full border border-dashed border-typography-300 my-2' />
        
        <VStack space='sm' className='w-full'>
          <Text size='md' className='text-typography-900'>
            <Text className='font-bold'>Amount:</Text> £
            {transactionSummary.amount.toFixed(2)}
          </Text>
          <Text size='md' className='text-typography-900'>
            <Text className='font-bold'>Points awarded:</Text>{' '}
            {transactionSummary.pointsAwarded}
          </Text>
          <Text size='md' className='text-typography-900'>
            <Text className='font-bold'>Points redeemed:</Text>{' '}
            {transactionSummary.pointsRedeemed}
          </Text>
          {transactionSummary.redeemedRewards.length > 0 && (
            <Text size='md' className='text-typography-900'>
              <Text className='font-bold'>Rewards redeemed:</Text>{' '}
              {transactionSummary.redeemedRewards.join(', ')}
            </Text>
          )}
        </VStack>
        
        <Box className='w-full border border-dashed border-typography-300 my-2' />
        
        <HStack space='md' className='w-full'>
          <Button
            size='lg'
            action='secondary'
            className='flex-1'
            onPress={onCancel}
            disabled={isProcessing}
          >
            <ButtonText>Cancel</ButtonText>
          </Button>
          <Button
            size='lg'
            action='primary'
            className='flex-1'
            onPress={onConfirm}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <Spinner size='small' color='white' />
            ) : (
              <ButtonText>Process transaction</ButtonText>
            )}
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
}
