import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { TransactionSummary } from '../../../hooks/composite/useScanScreenData';

interface SuccessSectionProps {
  transactionSummary: TransactionSummary;
  onScanAgain: () => void;
}

export function SuccessSection({
  transactionSummary,
  onScanAgain,
}: SuccessSectionProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <VStack space='lg' className='items-center'>
        <FontAwesome name='check-circle' size={64} color='#10B981' />
        
        <Heading
          size='lg'
          className='text-typography-900 font-semibold text-center'
        >
          Transaction successful!
        </Heading>
        
        <VStack space='sm' className='w-full items-center'>
          <Text size='md' className='text-typography-900 text-center'>
            <Text className='font-bold'>Amount:</Text> £
            {transactionSummary.amount.toFixed(2)}
          </Text>
          <Text size='md' className='text-typography-900 text-center'>
            <Text className='font-bold'>Points awarded:</Text>{' '}
            {transactionSummary.pointsAwarded}
          </Text>
          {transactionSummary.pointsRedeemed > 0 && (
            <Text size='md' className='text-typography-900 text-center'>
              <Text className='font-bold'>Points redeemed:</Text>{' '}
              {transactionSummary.pointsRedeemed}
            </Text>
          )}
          {transactionSummary.redeemedRewards.length > 0 && (
            <Text size='md' className='text-typography-900 text-center'>
              <Text className='font-bold'>Rewards redeemed:</Text>{' '}
              {transactionSummary.redeemedRewards.join(', ')}
            </Text>
          )}
        </VStack>
        
        <Button
          size='lg'
          action='primary'
          className='w-full'
          onPress={onScanAgain}
        >
          <ButtonText>Scan again</ButtonText>
        </Button>
      </VStack>
    </Box>
  );
}
