import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Input, InputField } from '@indie-points/ui-input';
import { Switch } from '@indie-points/ui-switch';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface ScanFormSectionProps {
  customerPoints: number | null;
  eligibleRewards: any[];
  rewardToggles: { [rewardId: string]: boolean };
  purchaseAmount: string;
  errorMessage: string | null;
  isProcessing: boolean;
  onRewardToggle: (rewardId: string, value: boolean) => void;
  onPurchaseAmountChange: (value: string) => void;
  onCancel: () => void;
  onConfirm: () => void;
}

export function ScanFormSection({
  customerPoints,
  eligibleRewards,
  rewardToggles,
  purchaseAmount,
  errorMessage,
  isProcessing,
  onRewardToggle,
  onPurchaseAmountChange,
  onCancel,
  onConfirm,
}: ScanFormSectionProps) {
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <VStack space='lg'>
        <Heading
          size='lg'
          className='text-typography-900 font-semibold text-center'
        >
          Customer transaction
        </Heading>

        {/* Customer Points Display */}
        <VStack space='sm' className='items-center'>
          <Text size='md' className='text-typography-900 font-medium'>
            Customer points available
          </Text>
          <Box className='bg-primary-500 rounded-xl px-4 py-2'>
            <Text size='lg' className='text-white font-bold'>
              {customerPoints?.toLocaleString() || '0'}
            </Text>
          </Box>
        </VStack>

        {/* Eligible Rewards */}
        <VStack space='md'>
          <Text size='md' className='text-typography-900 font-medium'>
            Available rewards
          </Text>
          {eligibleRewards.length > 0 ? (
            <VStack space='sm'>
              {eligibleRewards.map(reward => (
                <Box
                  key={reward.id}
                  className='bg-background-50 border-2 border-background-300 rounded-xl p-4'
                >
                  <HStack space='md' className='items-center justify-between'>
                    <VStack space='xs' className='flex-1'>
                      <Text
                        size='md'
                        className='text-typography-900 font-semibold'
                      >
                        {reward.title}
                      </Text>
                      <Text size='sm' className='text-typography-600'>
                        {reward.description}
                      </Text>
                      <Text
                        size='sm'
                        className='text-typography-700 font-medium'
                      >
                        {reward.pointsRequired} points required
                      </Text>
                    </VStack>
                    <Switch
                      value={rewardToggles[reward.id] || false}
                      onValueChange={value => onRewardToggle(reward.id, value)}
                    />
                  </HStack>
                </Box>
              ))}
            </VStack>
          ) : (
            <Text size='sm' className='text-typography-600 text-center'>
              No eligible rewards for this customer.
            </Text>
          )}
        </VStack>

        {/* Transaction Amount */}
        <VStack space='md'>
          <Text size='md' className='text-typography-900 font-medium'>
            Transaction amount (£)
          </Text>
          {errorMessage === 'Please enter a valid purchase amount.' && (
            <Text size='sm' className='text-error-500'>
              Please enter a valid transaction amount.
            </Text>
          )}
          {Object.values(rewardToggles).some(Boolean) && (
            <Text size='sm' className='text-typography-600'>
              Enter the final transaction amount after rewards have been
              redeemed.
            </Text>
          )}
          <Input>
            <InputField
              placeholder='0.00'
              value={purchaseAmount}
              onChangeText={onPurchaseAmountChange}
              keyboardType='decimal-pad'
              autoFocus={false}
            />
          </Input>
        </VStack>

        {/* Action Buttons */}
        <HStack space='md'>
          <Button
            size='lg'
            action='secondary'
            className='flex-1'
            onPress={onCancel}
          >
            <ButtonText>Cancel</ButtonText>
          </Button>
          <Button
            size='lg'
            action='primary'
            className='flex-1'
            onPress={onConfirm}
            disabled={isProcessing}
          >
            <ButtonText>Confirm</ButtonText>
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
}
