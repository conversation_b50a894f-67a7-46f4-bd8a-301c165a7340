import { useCallback, useState } from 'react';

import { createPurchaseTransaction } from '../../services/transactionService';
import { PurchaseTransactionRequest } from '../../services/types';

export function useCreatePurchaseTransaction() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createTransaction = useCallback(async (transactionData: PurchaseTransactionRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await createPurchaseTransaction(transactionData);
      if (result.error) {
        setError(result.error);
        return { data: null, error: result.error };
      }
      return { data: result.data, error: null };
    } catch (err: any) {
      const errorMessage = err?.message || 'An unexpected error occurred';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  return { createTransaction, loading, error };
}
