import { useCallback, useState } from 'react';

import { BusinessService } from '../../services/businessService';
import { BusinessProfile } from '../../services/types';

export function useUpsertBusinessProfile() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const upsertProfile = useCallback(
    async (
      userId: string,
      profileData: Omit<
        BusinessProfile,
        'id' | 'userId' | 'createdAt' | 'updatedAt'
      >
    ) => {
      setLoading(true);
      setError(null);
      try {
        const result = await BusinessService.upsertBusinessProfile(
          userId,
          profileData
        );
        if (result.error) {
          setError(result.error);
          return { data: null, error: result.error };
        }
        return { data: result.data, error: null };
      } catch (err: any) {
        const errorMessage = err?.message || 'An unexpected error occurred';
        setError(errorMessage);
        return { data: null, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return { upsertProfile, loading, error };
}
