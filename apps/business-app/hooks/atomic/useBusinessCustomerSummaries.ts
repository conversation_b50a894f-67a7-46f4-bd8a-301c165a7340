import { useCallback, useEffect, useState } from 'react';

import { CustomerService } from '../../services/customerService';
import { BusinessCustomerSummary } from '../../services/types';

export function useBusinessCustomerSummaries(businessId?: string) {
  const [data, setData] = useState<BusinessCustomerSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!businessId) {
      setData([]);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await CustomerService.getBusinessCustomerSummaries(businessId);
      setData(result.data || []);
      setError(result.error);
    } catch (err: any) {
      setData([]);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [businessId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
