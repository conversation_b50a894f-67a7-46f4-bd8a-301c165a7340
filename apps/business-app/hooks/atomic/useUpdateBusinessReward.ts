import { useCallback, useState } from 'react';

import { updateBusinessReward } from '../../services/rewardService';
import { BusinessReward } from '../../services/types';

export function useUpdateBusinessReward() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateReward = useCallback(
    async (
      rewardId: string,
      rewardData: Partial<
        Omit<BusinessReward, 'id' | 'businessId' | 'createdAt' | 'updatedAt'>
      >
    ) => {
      setLoading(true);
      setError(null);
      try {
        const result = await updateBusinessReward(rewardId, rewardData);
        if (result.error) {
          setError(result.error);
          return { data: null, error: result.error };
        }
        return { data: result.data, error: null };
      } catch (err: any) {
        const errorMessage = err?.message || 'An unexpected error occurred';
        setError(errorMessage);
        return { data: null, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return { updateReward, loading, error };
}
