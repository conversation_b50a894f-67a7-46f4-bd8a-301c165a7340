import { useCallback, useState } from 'react';

import { createBusinessReward } from '../../services/rewardService';
import { BusinessReward } from '../../services/types';

export function useCreateBusinessReward() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createReward = useCallback(
    async (
      businessId: string,
      rewardData: Omit<
        BusinessReward,
        'id' | 'businessId' | 'createdAt' | 'updatedAt'
      >
    ) => {
      setLoading(true);
      setError(null);
      try {
        const result = await createBusinessReward(businessId, rewardData);
        if (result.error) {
          setError(result.error);
          return { data: null, error: result.error };
        }
        return { data: result.data, error: null };
      } catch (err: any) {
        const errorMessage = err?.message || 'An unexpected error occurred';
        setError(errorMessage);
        return { data: null, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return { createReward, loading, error };
}
