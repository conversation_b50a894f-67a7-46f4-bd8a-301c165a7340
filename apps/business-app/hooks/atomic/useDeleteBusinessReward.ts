import { useCallback, useState } from 'react';

import { deleteBusinessReward } from '../../services/rewardService';

export function useDeleteBusinessReward() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteReward = useCallback(async (rewardId: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await deleteBusinessReward(rewardId);
      if (result.error) {
        setError(result.error);
        return { success: false, error: result.error };
      }
      return { success: true, error: null };
    } catch (err: any) {
      const errorMessage = err?.message || 'An unexpected error occurred';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  return { deleteReward, loading, error };
}
