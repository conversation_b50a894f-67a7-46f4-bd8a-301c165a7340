import { useCallback, useEffect, useState } from 'react';

import { BusinessService } from '../../services/businessService';
import { BusinessProfile } from '../../services/types';

export function useBusinessProfile(userId?: string) {
  const [data, setData] = useState<BusinessProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!userId) {
      setData(null);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await BusinessService.getBusinessProfile(userId);
      setData(result.data);
      setError(result.error);
    } catch (err: any) {
      setData(null);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
