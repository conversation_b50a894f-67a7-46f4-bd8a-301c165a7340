import { useCallback, useEffect, useState } from 'react';

import { getBusinessTransactionHistory } from '../../services/transactionService';
import { BusinessTransaction } from '../../services/types';

export function useBusinessTransactionHistory(
  businessId?: string,
  page: number = 1,
  pageSize: number = 100
) {
  const [data, setData] = useState<BusinessTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!businessId) {
      setData([]);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await getBusinessTransactionHistory(
        businessId,
        page,
        pageSize
      );
      setData(result.data || []);
      setError(result.error);
    } catch (err: any) {
      setData([]);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [businessId, page, pageSize]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
