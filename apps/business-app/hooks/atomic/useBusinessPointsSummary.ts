import { useCallback, useEffect, useState } from 'react';

import { PointsService } from '../../services/pointsService';
import { BusinessPointsSummary } from '../../services/types';

export function useBusinessPointsSummary(businessId?: string) {
  const [data, setData] = useState<BusinessPointsSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!businessId) {
      setData(null);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await PointsService.getBusinessPointsSummary(businessId);
      setData(result.data);
      setError(result.error);
    } catch (err: any) {
      setData(null);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [businessId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
