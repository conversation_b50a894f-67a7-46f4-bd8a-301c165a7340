import { useAuth } from '@indie-points/contexts';
import { useCallback, useState } from 'react';

import { useBusinessCustomerSummaries } from '../atomic/useBusinessCustomerSummaries';
import { useBusinessProfile } from '../atomic/useBusinessProfile';
import { useBusinessRewards } from '../atomic/useBusinessRewards';
import { useCreatePurchaseTransaction } from '../atomic/useCreatePurchaseTransaction';

// Types for customer QR code data
export interface CustomerQRCodeData {
  expiry: number;
  issuedAt: number;
  userId: string;
}

export interface TransactionSummary {
  amount: number;
  pointsAwarded: number;
  pointsRedeemed: number;
  redeemedRewards: string[];
}

export function useScanScreenData() {
  const { user } = useAuth();

  // Atomic hooks
  const businessProfile = useBusinessProfile(user?.id);
  const businessRewards = useBusinessRewards(businessProfile.data?.id);
  const customerSummaries = useBusinessCustomerSummaries(businessProfile.data?.id);
  const createTransaction = useCreatePurchaseTransaction();

  // Local state for scan workflow
  const [scannedData, setScannedData] = useState<CustomerQRCodeData | null>(null);
  const [customerPoints, setCustomerPoints] = useState<number | null>(null);
  const [eligibleRewards, setEligibleRewards] = useState<any[]>([]);
  const [rewardToggles, setRewardToggles] = useState<{ [rewardId: string]: boolean }>({});
  const [step, setStep] = useState<'form' | 'confirm' | 'complete'>('form');
  const [transactionSummary, setTransactionSummary] = useState<TransactionSummary | null>(null);

  // Parse customer QR code data
  const parseCustomerQRCodeData = useCallback((data: string): CustomerQRCodeData | null => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.userId && parsed.expiry && parsed.issuedAt) {
        // Check if QR code is expired
        const now = Date.now();
        if (now > parsed.expiry) {
          return null; // Expired QR code
        }
        return {
          userId: parsed.userId,
          expiry: parsed.expiry,
          issuedAt: parsed.issuedAt,
        };
      }
      return null;
    } catch (error) {
      console.error('Error parsing customer QR code data:', error);
      return null;
    }
  }, []);

  // Fetch customer points and eligible rewards
  const fetchCustomerPointsAndRewards = useCallback(async (customerId: string) => {
    if (!businessProfile.data || !businessRewards.data || !customerSummaries.data) return;

    // Find customer points
    const customerSummary = customerSummaries.data.find(c => c.customerId === customerId);
    const points = customerSummary?.activePoints || 0;
    setCustomerPoints(points);

    // Filter for eligible rewards
    const eligible = businessRewards.data.filter(r => points >= r.pointsRequired);
    // Sort eligible rewards by pointsRequired descending (highest first)
    eligible.sort((a, b) => b.pointsRequired - a.pointsRequired);
    setEligibleRewards(eligible);
    
    // Default toggles to false
    setRewardToggles(Object.fromEntries(eligible.map(r => [r.id, false])));
  }, [businessProfile.data, businessRewards.data, customerSummaries.data]);

  // Calculate points to be awarded
  const calculatePointsAwarded = useCallback((amount: number) => {
    return Math.floor(amount * 1); // 1 point per £1 spent
  }, []);

  // Calculate points to be redeemed
  const calculatePointsRedeemed = useCallback((selectedRewardIds: string[]) => {
    return selectedRewardIds.reduce((total, rewardId) => {
      const reward = eligibleRewards.find(r => r.id === rewardId);
      return total + (reward?.pointsRequired || 0);
    }, 0);
  }, [eligibleRewards]);

  // Reset scan state
  const resetScanState = useCallback(() => {
    setScannedData(null);
    setCustomerPoints(null);
    setEligibleRewards([]);
    setRewardToggles({});
    setStep('form');
    setTransactionSummary(null);
  }, []);

  // Process transaction
  const processTransaction = useCallback(async (
    amount: number,
    selectedRewardIds: string[]
  ) => {
    if (!scannedData || !businessProfile.data) {
      return { success: false, error: 'Missing required data' };
    }

    const result = await createTransaction.createTransaction({
      customerId: scannedData.userId,
      businessId: businessProfile.data.id,
      amountSpent: amount,
      qrToken: `${scannedData.userId}-${scannedData.issuedAt}`,
      rewardIds: selectedRewardIds.length > 0 ? selectedRewardIds : undefined,
    });

    return result;
  }, [scannedData, businessProfile.data, createTransaction.createTransaction]);

  return {
    // Data
    businessProfile: businessProfile.data,
    scannedData,
    customerPoints,
    eligibleRewards,
    rewardToggles,
    step,
    transactionSummary,

    // Loading states
    loading: businessProfile.loading,
    loadingRewards: businessRewards.loading,
    loadingCustomers: customerSummaries.loading,
    processingTransaction: createTransaction.loading,

    // Error states
    error: businessProfile.error || businessRewards.error || customerSummaries.error,

    // Actions
    setScannedData,
    setRewardToggles,
    setStep,
    setTransactionSummary,
    parseCustomerQRCodeData,
    fetchCustomerPointsAndRewards,
    calculatePointsAwarded,
    calculatePointsRedeemed,
    resetScanState,
    processTransaction,

    // Refetch functions
    refetchProfile: businessProfile.refetch,
    refetchRewards: businessRewards.refetch,
    refetchCustomers: customerSummaries.refetch,
  };
}
