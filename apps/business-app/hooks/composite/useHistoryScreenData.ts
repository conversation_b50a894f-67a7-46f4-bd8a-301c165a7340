import { useAuth } from '@indie-points/contexts';
import { useCallback, useEffect, useState } from 'react';

import { useBusinessCustomerSummaries } from '../atomic/useBusinessCustomerSummaries';
import { useBusinessProfile } from '../atomic/useBusinessProfile';
import { useBusinessTransactionHistory } from '../atomic/useBusinessTransactionHistory';

export type HistoryTab = 'transactions' | 'customers';

export function useHistoryScreenData() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<HistoryTab>('transactions');
  const [refreshing, setRefreshing] = useState(false);

  // Pagination state for transactions
  const [transactionPage, setTransactionPage] = useState(1);
  const [loadingMoreTransactions, setLoadingMoreTransactions] = useState(false);
  const [hasMoreTransactions, setHasMoreTransactions] = useState(true);
  const [allTransactions, setAllTransactions] = useState<any[]>([]);

  const PAGE_SIZE = 10;

  // Atomic hooks
  const businessProfile = useBusinessProfile(user?.id);
  const transactionHistory = useBusinessTransactionHistory(
    businessProfile.data?.id,
    transactionPage,
    PAGE_SIZE
  );
  const customerSummaries = useBusinessCustomerSummaries(
    businessProfile.data?.id
  );

  // Update all transactions when new data comes in
  useEffect(() => {
    if (transactionHistory.data) {
      if (transactionPage === 1) {
        // First page - replace all data
        setAllTransactions(transactionHistory.data);
      } else {
        // Subsequent pages - append data
        setAllTransactions(prev => [...prev, ...transactionHistory.data]);
      }

      // Check if we have more data
      setHasMoreTransactions(transactionHistory.data.length === PAGE_SIZE);
    }
  }, [transactionHistory.data, transactionPage]);

  // Load more transactions
  const loadMoreTransactions = useCallback(async () => {
    if (
      loadingMoreTransactions ||
      !hasMoreTransactions ||
      !businessProfile.data?.id
    )
      return;

    setLoadingMoreTransactions(true);
    const nextPage = transactionPage + 1;
    setTransactionPage(nextPage);

    // The useBusinessTransactionHistory hook will automatically refetch with the new page
    await transactionHistory.refetch();
    setLoadingMoreTransactions(false);
  }, [
    loadingMoreTransactions,
    hasMoreTransactions,
    businessProfile.data?.id,
    transactionPage,
    transactionHistory.refetch,
  ]);

  // Unified refresh for the screen
  const refresh = useCallback(async () => {
    setRefreshing(true);

    // Reset pagination
    setTransactionPage(1);
    setHasMoreTransactions(true);
    setAllTransactions([]);

    await Promise.all([
      businessProfile.refetch(),
      transactionHistory.refetch(),
      customerSummaries.refetch(),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);

    setRefreshing(false);
  }, [
    businessProfile.refetch,
    transactionHistory.refetch,
    customerSummaries.refetch,
  ]);

  // Handle tab change
  const handleTabChange = useCallback((tab: HistoryTab) => {
    setActiveTab(tab);
  }, []);

  // Determine overall loading state
  const loading =
    businessProfile.loading ||
    (activeTab === 'transactions' &&
      transactionHistory.loading &&
      transactionPage === 1) ||
    (activeTab === 'customers' && customerSummaries.loading);

  // Determine error state
  const error =
    businessProfile.error ||
    (activeTab === 'transactions'
      ? transactionHistory.error
      : customerSummaries.error);

  return {
    // Data
    businessProfile: businessProfile.data,
    transactions: allTransactions,
    customers: customerSummaries.data || [],

    // UI State
    activeTab,
    loading,
    error,
    refreshing,
    loadingMoreTransactions,
    hasMoreTransactions,

    // Actions
    setActiveTab: handleTabChange,
    refresh,
    loadMoreTransactions,

    // Refetch functions
    refetchProfile: businessProfile.refetch,
    refetchTransactions: transactionHistory.refetch,
    refetchCustomers: customerSummaries.refetch,
  };
}
