import { useAuth } from '@indie-points/contexts';

import { useBusinessProfile } from '../atomic/useBusinessProfile';

export function usePointsScreenData() {
  const { user } = useAuth();

  // Atomic hook for business profile (needed for QR code generation)
  const businessProfile = useBusinessProfile(user?.id);

  return {
    businessProfile: businessProfile.data,
    loading: businessProfile.loading,
    error: businessProfile.error,
    refetch: businessProfile.refetch,
  };
}
