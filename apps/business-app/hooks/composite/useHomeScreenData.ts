import { useAuth } from '@indie-points/contexts';
import { useCallback, useState } from 'react';

import { useBusinessPointsSummary } from '../atomic/useBusinessPointsSummary';
import { useBusinessProfile } from '../atomic/useBusinessProfile';

export function useHomeScreenData() {
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  // Atomic hooks
  const businessProfile = useBusinessProfile(user?.id);
  const pointsSummary = useBusinessPointsSummary(businessProfile.data?.id);

  // Unified refresh for the screen
  const refresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([
      businessProfile.refetch(),
      pointsSummary.refetch(),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  }, [businessProfile.refetch, pointsSummary.refetch]);

  // Determine overall loading state
  const loading =
    businessProfile.loading || (businessProfile.data && pointsSummary.loading);

  // Determine error state - prioritize business profile errors since they're more critical
  const error = businessProfile.error || pointsSummary.error;

  return {
    businessProfile: businessProfile.data,
    pointsData: pointsSummary.data,
    loading,
    error,
    refreshing,
    refresh,
  };
}
