import { useAuth } from '@indie-points/contexts';
import { useCallback, useState } from 'react';

import { useBusinessProfile } from '../atomic/useBusinessProfile';
import { useBusinessRewards } from '../atomic/useBusinessRewards';
import { useCreateBusinessReward } from '../atomic/useCreateBusinessReward';
import { useDeleteBusinessReward } from '../atomic/useDeleteBusinessReward';
import { useUpdateBusinessReward } from '../atomic/useUpdateBusinessReward';
import { useUpsertBusinessProfile } from '../atomic/useUpsertBusinessProfile';

export function useSettingsScreenData() {
  const { user } = useAuth();

  // Atomic hooks for data fetching
  const businessProfile = useBusinessProfile(user?.id);
  const businessRewards = useBusinessRewards(businessProfile.data?.id);

  // Atomic hooks for mutations
  const upsertProfile = useUpsertBusinessProfile();
  const createReward = useCreateBusinessReward();
  const updateReward = useUpdateBusinessReward();
  const deleteReward = useDeleteBusinessReward();

  // Refresh rewards after mutations
  const refreshRewards = useCallback(async () => {
    await businessRewards.refetch();
  }, [businessRewards.refetch]);

  // Refresh profile after mutations
  const refreshProfile = useCallback(async () => {
    await businessProfile.refetch();
  }, [businessProfile.refetch]);

  // Determine overall loading state
  const loading = businessProfile.loading;
  const loadingRewards = businessRewards.loading;

  // Determine error state
  const error = businessProfile.error || businessRewards.error;

  return {
    // Data
    businessProfile: businessProfile.data,
    rewards: businessRewards.data,
    
    // Loading states
    loading,
    loadingRewards,
    
    // Error states
    error,
    
    // Mutations
    upsertProfile: upsertProfile.upsertProfile,
    createReward: createReward.createReward,
    updateReward: updateReward.updateReward,
    deleteReward: deleteReward.deleteReward,
    
    // Mutation loading states
    savingProfile: upsertProfile.loading,
    savingReward: createReward.loading || updateReward.loading || deleteReward.loading,
    
    // Refresh functions
    refreshProfile,
    refreshRewards,
  };
}
