import { supabase } from '@indie-points/lib';

import { BusinessPointsSummary, ServiceResponse } from './types';

/**
 * Points service for handling business points summary and analytics
 */
export class PointsService {
  /**
   * Get business points summary
   * @param businessId - The business's UUID
   * @returns Promise with business points summary data or error
   */
  static async getBusinessPointsSummary(
    businessId: string
  ): Promise<ServiceResponse<BusinessPointsSummary>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_business_points_summary',
        {
          p_business_id: businessId,
        }
      );

      if (error) {
        console.error('Error fetching business points summary:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business points summary',
        };
      }

      // The function returns an array with a single object
      const summary = data?.[0];

      if (!summary) {
        return {
          data: {
            totalEarned: 0,
            totalActive: 0,
            totalRedeemed: 0,
          },
          error: null,
        };
      }

      return {
        data: {
          totalEarned: summary.total_earned || 0,
          totalActive: summary.total_active || 0,
          totalRedeemed: summary.total_redeemed || 0,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getBusinessPointsSummary:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
