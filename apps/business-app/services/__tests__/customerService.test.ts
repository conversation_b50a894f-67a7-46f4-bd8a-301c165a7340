import { supabase } from '@indie-points/lib';

import { CustomerService } from '../';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('CustomerService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBusinessCustomerSummaries', () => {
    it('should return customer summaries when successful', async () => {
      const mockData = [
        {
          customer_id: 'customer-1',
          customer_display_name: 'Alice',
          active_points: 10,
          earned_points: 20,
          redeemed_points: 5,
          last_transaction_date: '2024-06-20',
          total_transactions: 3,
          total_amount_spent: '100.50',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockData,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await CustomerService.getBusinessCustomerSummaries('mock-business-id');

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_business_customer_summaries',
        {
          p_business_id: 'mock-business-id',
        }
      );

      expect(result.data).toEqual([
        {
          customerId: 'customer-1',
          customerDisplayName: 'Alice',
          activePoints: 10,
          earnedPoints: 20,
          redeemedPoints: 5,
          lastTransactionDate: '2024-06-20',
          totalTransactions: 3,
          totalAmountSpent: 100.5,
        },
      ]);
      expect(result.error).toBeNull();
    });

    it('should return empty array when no data exists', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await CustomerService.getBusinessCustomerSummaries('mock-business-id');

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'Database connection failed',
          details: 'Connection refused',
          hint: 'Check your database connection',
          code: '500',
          name: 'DatabaseError',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await CustomerService.getBusinessCustomerSummaries('mock-business-id');

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await CustomerService.getBusinessCustomerSummaries('mock-business-id');

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });
  });
});
