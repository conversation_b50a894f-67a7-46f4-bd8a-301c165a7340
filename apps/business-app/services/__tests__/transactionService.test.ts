import { supabase } from '@indie-points/lib';

import {
  createPurchaseTransaction,
  getBusinessTransactionHistory,
} from '../transactionService';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('TransactionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBusinessTransactionHistory', () => {
    const mockBusinessId = 'test-business-id';

    it('should return transaction history when successful', async () => {
      const mockTransactionData = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: mockBusinessId,
          amount_spent: '50.00',
          points_awarded: 25,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
        {
          id: 'transaction-2',
          customer_id: 'customer-2',
          business_id: mockBusinessId,
          amount_spent: '30.00',
          points_awarded: 15,
          points_redeemed: 0,
          qr_token: 'qr-token-2',
          type: 'PURCHASE',
          created_at: '2024-01-14T00:00:00Z',
          updated_at: '2024-01-14T00:00:00Z',
        },
      ];

      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: mockTransactionData,
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toEqual([
        {
          id: 'transaction-1',
          customerId: 'customer-1',
          businessId: mockBusinessId,
          amountSpent: 50.0,
          pointsAwarded: 25,
          pointsRedeemed: 0,
          qrToken: 'qr-token-1',
          type: 'purchase',
          createdAt: '2024-01-15T00:00:00Z',
          updatedAt: '2024-01-15T00:00:00Z',
        },
        {
          id: 'transaction-2',
          customerId: 'customer-2',
          businessId: mockBusinessId,
          amountSpent: 30.0,
          pointsAwarded: 15,
          pointsRedeemed: 0,
          qrToken: 'qr-token-2',
          type: 'purchase',
          createdAt: '2024-01-14T00:00:00Z',
          updatedAt: '2024-01-14T00:00:00Z',
        },
      ]);
      expect(result.error).toBeNull();
    });

    it('should return empty array when no transactions exist', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Database connection failed' },
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle pagination parameters', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      await getBusinessTransactionHistory(mockBusinessId, 2, 50);

      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(mockSelect().eq).toHaveBeenCalledWith(
        'business_id',
        mockBusinessId
      );
      expect(mockSelect().eq().order).toHaveBeenCalledWith('created_at', {
        ascending: false,
      });
      expect(mockSelect().eq().order().range).toHaveBeenCalledWith(50, 99);
    });

    it('should handle unexpected errors', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockRejectedValue(new Error('Network error')),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });
  });

  describe('createPurchaseTransaction', () => {
    const mockTransactionData = {
      customerId: 'customer-1',
      businessId: 'business-1',
      amountSpent: 50.0,
      qrToken: 'qr-token-1',
    };

    it('should create purchase transaction when successful', async () => {
      const mockTransactionResult = [
        {
          purchase_transaction_id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '50.00',
          points_awarded: 50,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await createPurchaseTransaction(mockTransactionData);

      expect(result.data).toEqual({
        id: 'transaction-1',
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 50.0,
        pointsAwarded: 50,
        pointsRedeemed: 0,
        qrToken: 'qr-token-1',
        type: 'purchase',
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z',
      });
      expect(result.error).toBeNull();
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_purchase_transaction',
        {
          p_business_id: 'business-1',
          p_customer_id: 'customer-1',
          p_amount_spent: 50.0,
          p_points_awarded: 50,
          p_qr_token: 'qr-token-1',
          p_reward_ids: [],
        }
      );
    });

    it('should return error when no transaction data is returned', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await createPurchaseTransaction(mockTransactionData);

      expect(result.data).toBeNull();
      expect(result.error).toBe('No transaction data returned');
    });

    it('should return error when database query fails', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'Database connection failed',
          details: 'Connection refused',
          hint: 'Check your database connection',
          code: '500',
          name: 'DatabaseError',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result = await createPurchaseTransaction(mockTransactionData);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result = await createPurchaseTransaction(mockTransactionData);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });

    it('should calculate points correctly for whole pound amounts', async () => {
      const transactionData = {
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 25.0,
        qrToken: 'qr-token-1',
      };

      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '25.00',
          points_awarded: 25,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      await createPurchaseTransaction(transactionData);

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_purchase_transaction',
        {
          p_business_id: 'business-1',
          p_customer_id: 'customer-1',
          p_amount_spent: 25.0,
          p_points_awarded: 25,
          p_qr_token: 'qr-token-1',
          p_reward_ids: [],
        }
      );
    });

    it('should round up points for decimal amounts', async () => {
      const transactionData = {
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 25.5,
        qrToken: 'qr-token-1',
      };

      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '25.50',
          points_awarded: 26,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      await createPurchaseTransaction(transactionData);

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_purchase_transaction',
        {
          p_business_id: 'business-1',
          p_customer_id: 'customer-1',
          p_amount_spent: 25.5,
          p_points_awarded: 26,
          p_qr_token: 'qr-token-1',
          p_reward_ids: [],
        }
      );
    });

    it('should round up points for small decimal amounts', async () => {
      const transactionData = {
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 0.01,
        qrToken: 'qr-token-1',
      };

      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '0.01',
          points_awarded: 1,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      await createPurchaseTransaction(transactionData);

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_purchase_transaction',
        {
          p_business_id: 'business-1',
          p_customer_id: 'customer-1',
          p_amount_spent: 0.01,
          p_points_awarded: 1,
          p_qr_token: 'qr-token-1',
          p_reward_ids: [],
        }
      );
    });
  });

  describe('createPurchaseTransaction with redemptions', () => {
    const mockTransactionDataWithRedemptions = {
      customerId: 'customer-1',
      businessId: 'business-1',
      amountSpent: 50.0,
      qrToken: 'qr-token-1',
      rewardIds: ['reward-1', 'reward-2'],
    };

    it('should create purchase transaction with redemption when successful', async () => {
      const mockTransactionResult = [
        {
          purchase_transaction_id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '50.00',
          points_awarded: 50,
          points_redeemed: 100,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await createPurchaseTransaction(
        mockTransactionDataWithRedemptions
      );

      expect(result.data).toEqual({
        id: 'transaction-1',
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 50.0,
        pointsAwarded: 50,
        pointsRedeemed: 100,
        qrToken: 'qr-token-1',
        type: 'purchase',
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z',
      });
      expect(result.error).toBeNull();
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_purchase_transaction',
        {
          p_business_id: 'business-1',
          p_customer_id: 'customer-1',
          p_amount_spent: 50.0,
          p_points_awarded: 50,
          p_qr_token: 'qr-token-1',
          p_reward_ids: ['reward-1', 'reward-2'],
        }
      );
    });

    it('should return error when Supabase returns an error', async () => {
      const mockError = {
        message: 'Insufficient points for redemption',
        code: '400',
        hint: 'Customer does not have enough points',
        details: 'Required: 100, Available: 50',
        name: 'InsufficientPointsError',
      };

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: mockError,
        count: null,
        status: 400,
        statusText: 'Bad Request',
      });

      const result = await createPurchaseTransaction(
        mockTransactionDataWithRedemptions
      );

      expect(result.data).toBeNull();
      expect(result.error).toBe('Insufficient points for redemption');
    });

    it('should return error when no transaction data is returned', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await createPurchaseTransaction(
        mockTransactionDataWithRedemptions
      );

      expect(result.data).toBeNull();
      expect(result.error).toBe('No transaction data returned');
    });
  });
});
