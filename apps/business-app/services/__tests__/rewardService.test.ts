import { supabase } from '@indie-points/lib';

import * as RewardService from '../rewardService';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('RewardService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBusinessRewards', () => {
    const mockBusinessId = 'test-business-id';

    it('should return business rewards when successful', async () => {
      const mockRewardsData = [
        {
          id: 'reward-1',
          business_id: mockBusinessId,
          title: '£5 off',
          description: '£5 off your next transaction',
          points_required: 100,
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'reward-2',
          business_id: mockBusinessId,
          title: 'Free coffee',
          description: 'Get a free coffee',
          points_required: 50,
          is_active: true,
          created_at: '2024-01-02T00:00:00Z',
          updated_at: '2024-01-02T00:00:00Z',
        },
      ];

      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockRewardsData,
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await RewardService.getBusinessRewards(mockBusinessId);

      expect(result.data).toHaveLength(2);
      expect(result.data?.[0]).toEqual({
        id: 'reward-1',
        businessId: mockBusinessId,
        title: '£5 off',
        description: '£5 off your next transaction',
        pointsRequired: 100,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      });
      expect(result.error).toBeNull();
    });

    it('should return empty array when no rewards exist', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await RewardService.getBusinessRewards(mockBusinessId);

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Database connection failed' },
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await RewardService.getBusinessRewards(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });
  });

  describe('createBusinessReward', () => {
    const mockBusinessId = 'test-business-id';
    const mockRewardData = {
      title: '£5 off',
      description: '£5 off your next transaction',
      pointsRequired: 100,
      isActive: true,
    };

    it('should create business reward when successful', async () => {
      const mockCreatedReward = {
        id: 'new-reward-id',
        business_id: mockBusinessId,
        title: '£5 off',
        description: '£5 off your next transaction',
        points_required: 100,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: mockCreatedReward,
            error: null,
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        insert: mockInsert,
      } as any);

      const result = await RewardService.createBusinessReward(
        mockBusinessId,
        mockRewardData
      );

      expect(result.data).toEqual({
        id: 'new-reward-id',
        businessId: mockBusinessId,
        title: '£5 off',
        description: '£5 off your next transaction',
        pointsRequired: 100,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      });
      expect(result.error).toBeNull();
    });

    it('should return error when insert operation fails', async () => {
      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Insert failed' },
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        insert: mockInsert,
      } as any);

      const result = await RewardService.createBusinessReward(
        mockBusinessId,
        mockRewardData
      );

      expect(result.data).toBeNull();
      expect(result.error).toBe('Insert failed');
    });
  });
});
