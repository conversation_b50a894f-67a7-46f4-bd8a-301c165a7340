import { supabase } from '@indie-points/lib';

import {
  BusinessTransaction,
  PurchaseTransactionRequest,
  ServiceResponse,
} from './types';

/**
 * Get business transaction history
 * @param businessId - The business's UUID
 * @param page - Page number (default: 1)
 * @param pageSize - Number of transactions per page (default: 100)
 * @returns Promise with business transaction history data or error
 */
export async function getBusinessTransactionHistory(
  businessId: string,
  page: number = 1,
  pageSize: number = 100
): Promise<ServiceResponse<BusinessTransaction[]>> {
  try {
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('business_id', businessId)
      .order('created_at', { ascending: false })
      .range((page - 1) * pageSize, page * pageSize - 1);

    if (error) {
      console.error('Error fetching business transaction history:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch business transaction history',
      };
    }

    if (!data || !Array.isArray(data)) {
      return {
        data: [],
        error: null,
      };
    }

    // Map the database response to our expected format
    const transactions: BusinessTransaction[] = data.map((item: any) => ({
      id: item.id,
      customerId: item.customer_id,
      businessId: item.business_id,
      amountSpent: parseFloat(item.amount_spent || '0'),
      pointsAwarded: item.points_awarded || 0,
      pointsRedeemed: item.points_redeemed || 0,
      qrToken: item.qr_token,
      type: item.type?.toLowerCase() || 'purchase',
      createdAt: item.created_at,
      updatedAt: item.updated_at,
    }));

    return {
      data: transactions,
      error: null,
    };
  } catch (error) {
    console.error('Unexpected error in getBusinessTransactionHistory:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

/**
 * Create a purchase transaction with points and optional reward redemptions
 * @param transactionData - The purchase transaction data with optional reward redemptions
 * @returns Promise with created transaction data or error
 */
export async function createPurchaseTransaction(
  transactionData: PurchaseTransactionRequest
): Promise<ServiceResponse<BusinessTransaction>> {
  try {
    // Calculate points: 1 point per £1 spent, always rounded up
    const pointsAwarded = Math.ceil(transactionData.amountSpent);

    const { data, error } = await supabase.rpc('create_purchase_transaction', {
      p_business_id: transactionData.businessId,
      p_customer_id: transactionData.customerId,
      p_amount_spent: transactionData.amountSpent,
      p_points_awarded: pointsAwarded,
      p_qr_token: transactionData.qrToken,
      p_reward_ids: transactionData.rewardIds || [],
    });

    if (error) {
      console.error('Error creating purchase transaction:', error);
      return {
        data: null,
        error: error.message || 'Failed to create purchase transaction',
      };
    }

    // The function returns an array with a single object
    const transaction = data?.[0];

    if (!transaction) {
      return {
        data: null,
        error: 'No transaction data returned',
      };
    }

    return {
      data: {
        id: transaction.purchase_transaction_id,
        customerId: transaction.customer_id,
        businessId: transaction.business_id,
        amountSpent: parseFloat(transaction.amount_spent || '0'),
        pointsAwarded: transaction.points_awarded || 0,
        pointsRedeemed: transaction.points_redeemed || 0,
        qrToken: transaction.qr_token,
        type: transaction.type?.toLowerCase() || 'purchase',
        createdAt: transaction.created_at,
        updatedAt: transaction.updated_at,
      },
      error: null,
    };
  } catch (error) {
    console.error('Unexpected error in createPurchaseTransaction:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
