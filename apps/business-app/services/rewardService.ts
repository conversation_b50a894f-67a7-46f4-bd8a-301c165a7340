import { supabase } from '@indie-points/lib';

import { BusinessReward, ServiceResponse } from './types';

/**
 * Get business rewards
 * @param businessId - The business's UUID
 * @returns Promise with business rewards data or error
 */
export async function getBusinessRewards(
  businessId: string
): Promise<ServiceResponse<BusinessReward[]>> {
  try {
    const { data, error } = await supabase
      .from('business_rewards')
      .select('*')
      .eq('business_id', businessId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching business rewards:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch business rewards',
      };
    }

    if (!data || !Array.isArray(data)) {
      return {
        data: [],
        error: null,
      };
    }

    // Map the database response to our expected format
    const rewards: BusinessReward[] = data.map((item: any) => ({
      id: item.id,
      businessId: item.business_id,
      title: item.title,
      description: item.description,
      pointsRequired: item.points_required,
      isActive: item.is_active,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
    }));

    return {
      data: rewards,
      error: null,
    };
  } catch (error) {
    console.error('Unexpected error in getBusinessRewards:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

/**
 * Create a business reward
 * @param businessId - The business's UUID
 * @param rewardData - The reward data to create
 * @returns Promise with created reward data or error
 */
export async function createBusinessReward(
  businessId: string,
  rewardData: Omit<
    BusinessReward,
    'id' | 'businessId' | 'createdAt' | 'updatedAt'
  >
): Promise<ServiceResponse<BusinessReward>> {
  try {
    const { data, error } = await supabase
      .from('business_rewards')
      .insert({
        business_id: businessId,
        title: rewardData.title,
        description: rewardData.description,
        points_required: rewardData.pointsRequired,
        is_active: rewardData.isActive,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating business reward:', error);
      return {
        data: null,
        error: error.message || 'Failed to create business reward',
      };
    }

    return {
      data: {
        id: data.id,
        businessId: data.business_id,
        title: data.title,
        description: data.description,
        pointsRequired: data.points_required,
        isActive: data.is_active,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      },
      error: null,
    };
  } catch (error) {
    console.error('Unexpected error in createBusinessReward:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

/**
 * Update a business reward
 * @param rewardId - The reward's UUID
 * @param rewardData - The reward data to update
 * @returns Promise with updated reward data or error
 */
export async function updateBusinessReward(
  rewardId: string,
  rewardData: Partial<
    Omit<BusinessReward, 'id' | 'businessId' | 'createdAt' | 'updatedAt'>
  >
): Promise<ServiceResponse<BusinessReward>> {
  try {
    const updateData: any = {};
    if (rewardData.title !== undefined) updateData.title = rewardData.title;
    if (rewardData.description !== undefined)
      updateData.description = rewardData.description;
    if (rewardData.pointsRequired !== undefined)
      updateData.points_required = rewardData.pointsRequired;
    if (rewardData.isActive !== undefined)
      updateData.is_active = rewardData.isActive;

    const { data, error } = await supabase
      .from('business_rewards')
      .update(updateData)
      .eq('id', rewardId)
      .select()
      .single();

    if (error) {
      console.error('Error updating business reward:', error);
      return {
        data: null,
        error: error.message || 'Failed to update business reward',
      };
    }

    return {
      data: {
        id: data.id,
        businessId: data.business_id,
        title: data.title,
        description: data.description,
        pointsRequired: data.points_required,
        isActive: data.is_active,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      },
      error: null,
    };
  } catch (error) {
    console.error('Unexpected error in updateBusinessReward:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

/**
 * Delete a business reward (soft delete by setting is_active to false)
 * @param rewardId - The reward's UUID
 * @returns Promise with success status or error
 */
export async function deleteBusinessReward(
  rewardId: string
): Promise<ServiceResponse<boolean>> {
  try {
    const { error } = await supabase
      .from('business_rewards')
      .update({ is_active: false })
      .eq('id', rewardId);

    if (error) {
      console.error('Error deleting business reward:', error);
      return {
        data: null,
        error: error.message || 'Failed to delete business reward',
      };
    }

    return {
      data: true,
      error: null,
    };
  } catch (error) {
    console.error('Unexpected error in deleteBusinessReward:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
