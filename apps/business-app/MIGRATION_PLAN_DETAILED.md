# 🏗️ Detailed Migration Plan for business-app

## 1. **Current State Inventory**

### **A. Services and Their Methods**

**File:** `services/businessService.ts`  
**Types:** Defined in `services/types.ts`

#### **BusinessService Methods**

- `getBusinessProfile(userId: string): Promise<ServiceResponse<BusinessProfile>>`
- `upsertBusinessProfile(userId: string, profileData): Promise<ServiceResponse<BusinessProfile>>`
- `getBusinessPointsSummary(businessId: string): Promise<ServiceResponse<BusinessPointsSummary>>`
- `getBusinessCustomerSummaries(businessId: string): Promise<ServiceResponse<BusinessCustomerSummary[]>>`
- `getBusinessTransactionHistory(businessId: string, page?: number, pageSize?: number): Promise<ServiceResponse<BusinessTransaction[]>>`
- `createPurchaseTransaction(transactionData: PurchaseTransactionRequest): Promise<ServiceResponse<BusinessTransaction>>`
- `getBusinessRewards(businessId: string): Promise<ServiceResponse<BusinessReward[]>>`
- `createBusinessReward(businessId: string, rewardData): Promise<ServiceResponse<BusinessReward>>`
- `updateBusinessReward(rewardId: string, rewardData): Promise<ServiceResponse<BusinessReward>>`
- `deleteBusinessReward(rewardId: string): Promise<ServiceResponse<boolean>>`

#### **Service Types**

- `BusinessProfile`
- `BusinessReward`
- `BusinessPointsSummary`
- `BusinessCustomerSummary`
- `BusinessTransaction`
- `PurchaseTransactionRequest`
- `ServiceResponse<T>`

---

### **B. Screens / Page Components**

**Located in `app/` and `app/(tabs)/` (excluding auth):**

- `app/index.tsx` — Splash/entry screen
- `app/(tabs)/index.tsx` — Home (business points summary)
- `app/(tabs)/points.tsx` — Points/QR code for business
- `app/(tabs)/scan.tsx` — Scan customer QR code
- `app/(tabs)/settings.tsx` — Business profile, rewards management
- `app/(tabs)/history/index.tsx` — Transaction and customer history

---

### **C. UI Components**

- No dedicated `components/` directory found; UI is currently implemented directly in screens.
- Utility functions in `utils/printUtils.ts` (e.g., for printing QR codes).

---

### **D. Hooks**

- No atomic or composite hooks found. All data fetching and state management is currently handled
  directly in screens.

---

## 2. **Target Architecture Mapping**

| Layer            | Directory/Pattern              | Example(s)                                                          |
| ---------------- | ------------------------------ | ------------------------------------------------------------------- |
| **Service**      | `services/`                    | BusinessService (to be split into multiple)                         |
| **Atomic Hook**  | `hooks/atomic/`                | (To be created: useBusinessProfile, useBusinessPointsSummary, etc.) |
| **Composite**    | `hooks/composite/`             | (To be created: useHomeScreenData, usePointsScreenData, etc.)       |
| **Screen**       | `app/` and subfolders          | (tabs)/index.tsx, (tabs)/points.tsx, etc.                           |
| **UI Component** | (To be created: `components/`) | (To be extracted: BusinessList, PointsCard, etc.)                   |

---

## 3. **Step-by-Step Migration Plan**

### **PHASE 0: Workspace Analysis & Preparation**

- [x] Confirm all screens, services, and utility files are inventoried.
- [x] Create a `MIGRATION_LOG.md` to track progress.

---

### **PHASE 1: Service Layer Modularization (NEW)**

## Status: **COMPLETE** (2024-06-21)

All business logic has been migrated from the monolithic BusinessService to focused,
single-responsibility services:

- Profile logic unified under BusinessService (ProfileService removed)
- Points logic in PointsService
- Customer summary logic in CustomerService
- Transaction logic in TransactionService
- Reward logic in RewardService
- All references and tests updated
- All type checks and tests pass, no regressions

**Phase one is finished. The codebase is now ready for the next phase.**

**Goal:** Refactor the monolithic `BusinessService` into multiple focused services for better
separation of concerns and maintainability.

| **Proposed Service Structure:**              | Service Name                     | File Name                         | Responsibilities        |
| -------------------------------------------- | -------------------------------- | --------------------------------- | ----------------------- | -------------------- |
| ProfileService                               | `profileService.ts`              | Business profile CRUD, onboarding |                         | PointsService        |
| `pointsService.ts`                           | Points summary, points analytics |                                   | CustomerService         | `customerService.ts` |
| Customer summaries, customer analytics       |                                  | TransactionService                | `transactionService.ts` |
| Transaction history, purchase/visit creation |                                  | RewardService                     | `rewardService.ts`      | Rewards CRUD         |
| (create, update, delete, list)               |

**Migration Steps:**

- [x] Create a new file for each service in `services/` (e.g., `profileService.ts`,
      `pointsService.ts`, `customerService.ts`).
- [x] Move each method from `BusinessService` to the appropriate new service (profile, points,
      customer summaries).
- [x] Refactor method signatures and imports as needed for these services.
- [x] Update types and interfaces if any are too tightly coupled to the old monolith.
- [x] Update all screens, hooks, and tests to import from the new service files for these services.
- [x] Update `services/index.ts` to export all new services.
- [x] Move and update all related tests for these services.
- [x] Test thoroughly and update/add tests as needed for these services.
- [x] Continue with TransactionService and RewardService.

---

### **PHASE 2: Atomic Hooks Refactor**

- [ ] For each service method, create a corresponding atomic hook in `hooks/atomic/`:
  - `useBusinessProfile(userId)`
  - `useBusinessPointsSummary(businessId)`
  - `useBusinessCustomerSummaries(businessId)`
  - `useBusinessTransactionHistory(businessId, page, pageSize)`
  - `useBusinessRewards(businessId)`
  - `useCreatePurchaseTransaction()`
  - `useCreateBusinessReward()`
  - `useUpdateBusinessReward()`
  - `useDeleteBusinessReward()`
- [ ] Each hook manages its own `loading`, `error`, `data` state, and exposes a `refetch` or
      mutation method.

---

### **PHASE 3: Composite Hooks Creation**

- [ ] For each screen, create a composite hook in `hooks/composite/` that orchestrates the atomic
      hooks:
  - `useHomeScreenData` (for business summary)
  - `usePointsScreenData` (for QR code and points)
  - `useScanScreenData` (for scanning and transaction creation)
  - `useSettingsScreenData` (for profile and rewards management)
  - `useHistoryScreenData` (for transactions and customer history)
- [ ] Composite hooks provide unified loading/error/refresh logic.

---

### **PHASE 4: UI Component Purification**

- [ ] Extract presentational components from screens into a new `components/` directory:
  - BusinessList, CustomerList, PointsCard, QRCodeCard, RewardsList, etc.
- [ ] Ensure all UI components are pure and stateless, receiving data and callbacks via props.

---

### **PHASE 5: Screen Refactoring**

- [ ] Refactor each screen to use only composite hooks for data.
- [ ] Remove all direct data fetching, business logic, and state management from screens.
- [ ] Pass all data, loading, error, and callbacks to UI components.

---

### **PHASE 6: Testing & Validation**

- [ ] Write or update unit tests for services, atomic hooks, composite hooks, and UI components.
- [ ] Write integration tests for screens using mocked hooks.
- [ ] Validate all flows in the app manually and via automated tests.

---

### **PHASE 7: Documentation & Cleanup**

- [ ] Add JSDoc/type comments to all services, hooks, and components.
- [ ] Update or create README files for each major directory.
- [ ] Summarize the migration in `MIGRATION_LOG.md`.

---

## 4. **Migration Checklist**

- [x] PHASE 0: Workspace Analysis & Preparation complete
- [x] ProfileService, PointsService, CustomerService modularized (code and tests moved)
- [ ] All API/business logic in modular services in `services/`
- [ ] All data fetching/mutation via atomic hooks in `hooks/atomic/`
- [ ] All screens use composite hooks in `hooks/composite/`
- [ ] All UI components are pure and stateless in `components/`
- [ ] All screens are "thin" and only handle layout/navigation
- [ ] All layers are unit/integration tested
- [ ] Documentation is up to date

---

## 5. **Next Steps**

Would you like to begin with the service modularization phase, or proceed to atomic hooks or UI
component extraction after that?  
This plan can be reviewed and updated collaboratively as migration progresses.
