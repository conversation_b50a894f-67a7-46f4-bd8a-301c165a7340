import { Box } from '@indie-points/ui-box';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { VStack } from '@indie-points/ui-vstack';
import * as Haptics from 'expo-haptics';
import React from 'react';
import { ScrollView } from 'react-native';

import { CustomersList } from '../../../components/(tabs)/history/CustomersList';
import { HeaderSection } from '../../../components/(tabs)/history/HeaderSection';
import { HistoryErrorState } from '../../../components/(tabs)/history/HistoryErrorState';
import { HistoryLoadingState } from '../../../components/(tabs)/history/HistoryLoadingState';
import { TabSelector } from '../../../components/(tabs)/history/TabSelector';
import { TransactionsList } from '../../../components/(tabs)/history/TransactionsList';
import { useHistoryScreenData } from '../../../hooks/composite/useHistoryScreenData';

export default function History() {
  const {
    businessProfile,
    transactions,
    customers,
    activeTab,
    loading,
    error,
    refreshing,
    loadingMoreTransactions,
    hasMoreTransactions,
    setActiveTab,
    refresh,
    loadMoreTransactions,
  } = useHistoryScreenData();

  const handleTabChange = (tab: 'transactions' | 'customers') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setActiveTab(tab);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={refresh} />
        }
      >
        <HeaderSection />
        <Box className='px-6 pb-8'>
          {loading ? (
            <HistoryLoadingState />
          ) : error ? (
            <HistoryErrorState error={error} />
          ) : (
            <VStack space='lg'>
              <TabSelector
                activeTab={activeTab}
                onTabChange={handleTabChange}
              />

              {activeTab === 'transactions' ? (
                <TransactionsList
                  transactions={transactions}
                  loadingMore={loadingMoreTransactions}
                  hasMore={hasMoreTransactions}
                  onLoadMore={loadMoreTransactions}
                />
              ) : (
                <CustomersList customers={customers} />
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}

  const fetchData = useCallback(
    async (isRefresh = false) => {
      if (!user?.id) {
        if (!isRefresh) setLoading(false);
        return;
      }

      try {
        // First get the business profile
        const profileResult = await BusinessService.getBusinessProfile(user.id);

        if (profileResult.error) {
          setError(profileResult.error);
          if (!isRefresh) setLoading(false);
          return;
        }

        if (!profileResult.data) {
          setError(
            'No business profile found. Please complete your business setup in Settings.'
          );
          if (!isRefresh) setLoading(false);
          return;
        }

        // Set business ID for future requests
        setBusinessId(profileResult.data.id);

        // Reset pagination state
        if (isRefresh) {
          setTransactionPage(1);
          setHasMoreTransactions(true);
        }

        // Fetch data based on active tab
        if (activeTab === 'transactions') {
          await fetchTransactions(1, isRefresh);
        } else {
          await fetchCustomers(isRefresh);
        }
      } catch (err) {
        setError('An unexpected error occurred while fetching business data');
        console.error('Error fetching business history data:', err);
        if (!isRefresh) setLoading(false);
      }
    },
    [user?.id, activeTab, fetchTransactions, fetchCustomers]
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle tab change
  useEffect(() => {
    if (businessId) {
      if (activeTab === 'transactions') {
        fetchTransactions(1);
      } else {
        fetchCustomers();
      }
    }
  }, [activeTab, businessId, fetchTransactions, fetchCustomers]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchData(true),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  const loadMoreTransactions = async () => {
    if (loadingMoreTransactions || !hasMoreTransactions || !businessId) return;

    const nextPage = transactionPage + 1;
    setTransactionPage(nextPage);
    await fetchTransactions(nextPage);
  };

  const renderTransactionItem = (item: BusinessTransaction) => (
    <Box
      key={item.id}
      className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'
    >
      <HStack space='md' className='items-center'>
        {/* Icon */}
        <Box
          className={`w-12 h-12 rounded-xl items-center justify-center border-2 ${
            item.type === 'purchase'
              ? 'bg-primary-500 border-primary-700'
              : item.type === 'redemption'
                ? 'bg-error-500 border-error-700'
                : 'bg-secondary-500 border-secondary-700'
          }`}
        >
          <FontAwesome
            name={
              item.type === 'purchase'
                ? 'shopping-bag'
                : item.type === 'redemption'
                  ? 'gift'
                  : 'eye'
            }
            size={20}
            color='white'
          />
        </Box>

        {/* Content */}
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Customer #{item.customerId.slice(-4)}
          </Text>
          <Text size='sm' className='text-typography-600'>
            {item.type === 'purchase'
              ? 'Purchase'
              : item.type === 'redemption'
                ? 'Redemption'
                : 'Visit'}{' '}
            • £{item.amountSpent.toFixed(2)}
          </Text>
          <Text size='xs' className='text-typography-500'>
            {dayjs(item.createdAt).format('DD MMM YYYY, HH:mm')}
          </Text>
        </VStack>

        {/* Points */}
        <VStack className='items-end'>
          <Text
            size='lg'
            className={`font-bold ${
              item.type === 'redemption' ? 'text-error-500' : 'text-primary-500'
            }`}
          >
            {item.type === 'redemption'
              ? `-${item.pointsRedeemed} pts`
              : `+${item.pointsAwarded} pts`}
          </Text>
        </VStack>
      </HStack>
    </Box>
  );

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;

          if (
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - paddingToBottom
          ) {
            // User has scrolled to the bottom
            if (activeTab === 'transactions') {
              loadMoreTransactions();
            }
          }
        }}
        scrollEventThrottle={400}
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            History
          </Heading>

          {/* Colored divider line */}
          <GradientBar />

          {/* Tab Toggle */}
          <HStack className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg overflow-hidden'>
            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === 'transactions' ? 'bg-primary-500' : 'bg-white'
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                setActiveTab('transactions');
              }}
            >
              <Text
                size='md'
                className={`font-semibold text-center ${
                  activeTab === 'transactions'
                    ? 'text-white'
                    : 'text-typography-900'
                }`}
              >
                Transactions
              </Text>
            </Pressable>

            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === 'customers' ? 'bg-primary-500' : 'bg-white'
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                setActiveTab('customers');
              }}
            >
              <Text
                size='md'
                className={`font-semibold text-center ${
                  activeTab === 'customers'
                    ? 'text-white'
                    : 'text-typography-900'
                }`}
              >
                Customers
              </Text>
            </Pressable>
          </HStack>
        </VStack>

        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            <Box className='flex-1 items-center justify-center py-12'>
              <ActivityIndicator size='large' color='#3B82F6' />
              <Text size='md' className='text-typography-600 mt-4'>
                Loading history...
              </Text>
            </Box>
          ) : error ? (
            <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
              <HStack space='md' className='items-center'>
                <FontAwesome
                  name='exclamation-triangle'
                  size={20}
                  color='#EF4444'
                />
                <VStack className='flex-1'>
                  <Text size='md' className='text-error-700 font-semibold'>
                    Error loading data
                  </Text>
                  <Text size='sm' className='text-error-600'>
                    {error}
                  </Text>
                </VStack>
              </HStack>
            </Box>
          ) : activeTab === 'transactions' ? (
            <VStack space='md'>
              {transactionData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='history' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No transactions found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Business transactions will appear here once customers start
                    making purchases
                  </Text>
                </Box>
              ) : (
                <>
                  {transactionData.map(renderTransactionItem)}

                  {/* Loading more indicator */}
                  {loadingMoreTransactions && (
                    <Box className='py-4 items-center'>
                      <ActivityIndicator size='small' color='#3B82F6' />
                      <Text size='sm' className='text-typography-600 mt-2'>
                        Loading more transactions...
                      </Text>
                    </Box>
                  )}

                  {/* End of list indicator */}
                  {!hasMoreTransactions && transactionData.length > 0 && (
                    <Box className='py-4 items-center'>
                      <Text size='sm' className='text-typography-500'>
                        You have reached the end of your transaction history
                      </Text>
                    </Box>
                  )}
                </>
              )}
            </VStack>
          ) : (
            <VStack space='md'>
              {customerData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='users' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No customers found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Customers who visit your business will appear here
                  </Text>
                </Box>
              ) : (
                customerData.map(customer => (
                  <Box
                    key={customer.customerId}
                    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'
                  >
                    <HStack space='md' className='items-center'>
                      {/* Icon */}
                      <Box className='w-12 h-12 bg-primary-500 border-2 border-primary-700 rounded-xl items-center justify-center'>
                        <FontAwesome name='user' size={20} color='white' />
                      </Box>

                      {/* Content */}
                      <VStack className='flex-1'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          {customer.customerDisplayName}
                        </Text>
                        <Text size='sm' className='text-typography-600'>
                          {customer.totalTransactions} transactions • £
                          {customer.totalAmountSpent.toFixed(2)} spent
                        </Text>
                        <Text size='xs' className='text-typography-500'>
                          Last visit:{' '}
                          {dayjs(customer.lastTransactionDate).format(
                            'DD MMM YYYY, HH:mm'
                          )}
                        </Text>
                      </VStack>

                      {/* Points and Stats */}
                      <VStack className='items-end'>
                        <Text size='lg' className='text-primary-500 font-bold'>
                          {customer.activePoints} pts
                        </Text>
                        <Text size='xs' className='text-typography-500'>
                          {customer.earnedPoints} earned
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>
                ))
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
