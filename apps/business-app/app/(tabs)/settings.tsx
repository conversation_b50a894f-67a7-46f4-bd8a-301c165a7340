import { useAuth } from '@indie-points/contexts';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, ScrollView } from 'react-native';

import { BusinessProfileCard } from '../../components/(tabs)/settings/BusinessProfileCard';
import { ErrorMessage } from '../../components/(tabs)/settings/ErrorMessage';
import { HeaderSection } from '../../components/(tabs)/settings/HeaderSection';
import { RewardEditModal } from '../../components/(tabs)/settings/RewardEditModal';
import { RewardsManagementCard } from '../../components/(tabs)/settings/RewardsManagementCard';
import { SettingsLoadingState } from '../../components/(tabs)/settings/SettingsLoadingState';
import { SignOutButton } from '../../components/(tabs)/settings/SignOutButton';
import { UserProfileCard } from '../../components/(tabs)/settings/UserProfileCard';
import { useSettingsScreenData } from '../../hooks/composite/useSettingsScreenData';
import { BusinessReward } from '../../services/types';

export default function Settings() {
  const { user, signOut } = useAuth();
  const {
    businessProfile,
    rewards,
    loading,
    loadingRewards,
    error,
    upsertProfile,
    createReward,
    updateReward,
    deleteReward,
    savingProfile,
    savingReward,
    refreshProfile,
    refreshRewards,
  } = useSettingsScreenData();

  // Local UI state
  const [editing, setEditing] = useState(false);
  const [businessName, setBusinessName] = useState('');
  const [businessType, setBusinessType] = useState('');
  const [showRewardsPanel, setShowRewardsPanel] = useState(true);
  const [showAddReward, setShowAddReward] = useState(false);
  const [editingReward, setEditingReward] = useState<BusinessReward | null>(
    null
  );
  const [rewardTitle, setRewardTitle] = useState('');
  const [rewardDescription, setRewardDescription] = useState('');
  const [rewardPoints, setRewardPoints] = useState('');

  // Initialize form when business profile changes
  React.useEffect(() => {
    if (businessProfile) {
      setBusinessName(businessProfile.businessName);
      setBusinessType(businessProfile.businessType);
    } else if (!loading) {
      // No profile exists, start in editing mode
      setBusinessName('');
      setBusinessType('');
      setEditing(true);
    }
  }, [businessProfile, loading]);

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    const name = businessName.trim();
    const type = businessType.trim();

    if (!name || !type) {
      return;
    }

    const result = await upsertProfile(user.id, {
      businessName: name,
      businessType: type,
      onboardingCompleted: true,
      onboardingStep: 4,
    });

    if (result.data) {
      setEditing(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      await refreshProfile();
      await refreshRewards();
    }
  };

  const handleAddReward = () => {
    setShowRewardsPanel(false);
    setShowAddReward(true);
    setEditingReward(null);
    setRewardTitle('');
    setRewardDescription('');
    setRewardPoints('');
  };

  const handleEditReward = (reward: BusinessReward) => {
    setShowRewardsPanel(false);
    setEditingReward(reward);
    setShowAddReward(true);
    setRewardTitle(reward.title);
    setRewardDescription(reward.description);
    setRewardPoints(reward.pointsRequired.toString());
  };

  const handleSaveReward = async () => {
    if (!businessProfile?.id) return;

    const title = rewardTitle.trim();
    const description = rewardDescription.trim();
    const points = parseInt(rewardPoints);

    if (!title || !description || isNaN(points) || points <= 0) {
      return;
    }

    let result;
    if (editingReward) {
      // Update existing reward
      result = await updateReward(editingReward.id, {
        title,
        description,
        pointsRequired: points,
        isActive: true,
      });
    } else {
      // Create new reward
      result = await createReward(businessProfile.id, {
        title,
        description,
        pointsRequired: points,
        isActive: true,
      });
    }

    if (result.data) {
      setShowAddReward(false);
      setShowRewardsPanel(true);
      setEditingReward(null);
      await refreshRewards();
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };

  const handleDeleteReward = async (rewardId: string) => {
    Alert.alert(
      'Delete reward',
      'Are you sure you want to delete this reward?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const result = await deleteReward(rewardId);
            if (result.success) {
              setShowAddReward(false);
              setShowRewardsPanel(true);
              setEditingReward(null);
              await refreshRewards();
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
            }
          },
        },
      ]
    );
  };

  const handleSignOut = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Sign out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign out',
        style: 'destructive',
        onPress: async () => {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          const { error } = await signOut();
          if (!error) {
            router.replace('/(auth)/sign-in');
          }
        },
      },
    ]);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        <HeaderSection />
        <Box className='flex-1 px-6 pb-8'>
          {loading ? (
            <SettingsLoadingState />
          ) : (
            <VStack space='xl' className='flex-1'>
              <UserProfileCard userEmail={user?.email} />

              {error && <ErrorMessage error={error} />}

              <BusinessProfileCard
                businessProfile={businessProfile}
                editing={editing}
                businessName={businessName}
                businessType={businessType}
                saving={savingProfile}
                onEdit={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  setEditing(true);
                }}
                onCancel={() => {
                  setEditing(false);
                  // Reset form to original values
                  if (businessProfile) {
                    setBusinessName(businessProfile.businessName);
                    setBusinessType(businessProfile.businessType);
                  }
                }}
                onSave={handleSaveProfile}
                onBusinessNameChange={setBusinessName}
                onBusinessTypeChange={setBusinessType}
              />

              {businessProfile && showRewardsPanel && (
                <RewardsManagementCard
                  rewards={rewards}
                  loadingRewards={loadingRewards}
                  onAddReward={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                    handleAddReward();
                  }}
                  onEditReward={reward => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    handleEditReward(reward);
                  }}
                />
              )}

              {showAddReward && (
                <RewardEditModal
                  editingReward={editingReward}
                  rewardTitle={rewardTitle}
                  rewardDescription={rewardDescription}
                  rewardPoints={rewardPoints}
                  saving={savingReward}
                  onCancel={() => {
                    setShowAddReward(false);
                    setShowRewardsPanel(true);
                    setEditingReward(null);
                  }}
                  onSave={handleSaveReward}
                  onDelete={
                    editingReward
                      ? () => {
                          Haptics.impactAsync(
                            Haptics.ImpactFeedbackStyle.Light
                          );
                          handleDeleteReward(editingReward.id);
                        }
                      : undefined
                  }
                  onTitleChange={setRewardTitle}
                  onDescriptionChange={setRewardDescription}
                  onPointsChange={setRewardPoints}
                />
              )}

              <SignOutButton onSignOut={handleSignOut} />
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
