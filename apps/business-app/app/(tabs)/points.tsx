import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import * as Print from 'expo-print';
import React, { useCallback, useRef, useState } from 'react';
import { <PERSON><PERSON>, ScrollView } from 'react-native';

import { BusinessQRCodeSection } from '../../components/(tabs)/points/BusinessQRCodeSection';
import { HeaderSection } from '../../components/(tabs)/points/HeaderSection';
import { HowToUseSection } from '../../components/(tabs)/points/HowToUseSection';
import { QRCodeErrorState } from '../../components/(tabs)/points/QRCodeErrorState';
import { QRCodeLoadingState } from '../../components/(tabs)/points/QRCodeLoadingState';
import { usePointsScreenData } from '../../hooks/composite/usePointsScreenData';

export default function Points() {
  const { businessProfile, loading, error } = usePointsScreenData();
  const [isPrinting, setIsPrinting] = useState(false);
  const [qrCodeBase64, setQrCodeBase64] = useState<string | null>(null);
  const qrRef = useRef<any>(null);

  // Handle QR ref from child component
  const handleQRRef = useCallback((ref: any) => {
    qrRef.current = ref;
  }, []);

  // Generate base64 QR code for printing
  const generateQRBase64 = useCallback(() => {
    if (qrRef.current && businessProfile) {
      qrRef.current.toDataURL((dataURL: string) => {
        setQrCodeBase64(dataURL);
      });
    }
  }, [businessProfile]);

  // Create HTML content for printing
  const createPrintHTML = (): string => {
    if (!businessProfile?.businessName || !qrCodeBase64) return '';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Indie Points - Visit QR Code</title>
          <style>
            @page {
              size: A4;
              margin: 40px;
            }
            @media print {
              .no-print {
                display: none !important;
              }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 0;
              background: white;
              color: #000000;
              line-height: 1.6;
            }
            .container {
              max-width: 100%;
              margin: 0 auto;
              text-align: center;
              padding: 20px;
            }
            .brand-bar {
              display: flex;
              height: 8px;
              margin-bottom: 30px;
              border: 2px solid #000000;
            }
            .brand-bar-blue {
              flex: 1;
              background-color: #3182CE !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .brand-bar-yellow {
              flex: 1;
              background-color: #D69E2E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .brand-bar-red {
              flex: 1;
              background-color: #E53E3E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .title {
              font-size: 48px;
              font-weight: bold;
              margin-bottom: 10px;
              color: #000000;
            }
            .subtitle {
              font-size: 24px;
              color: #718096;
              margin-bottom: 40px;
            }
            .business-name {
              font-size: 32px;
              font-weight: bold;
              color: #3182CE;
              margin-bottom: 30px;
            }
            .qr-container {
              margin: 40px 0;
              display: flex;
              justify-content: center;
            }
            .instructions {
              font-size: 20px;
              color: #2D3748;
              margin-top: 40px;
              text-align: left;
              max-width: 600px;
              margin-left: auto;
              margin-right: auto;
            }
            .instruction-step {
              margin-bottom: 15px;
              padding-left: 30px;
              position: relative;
            }
            .step-number {
              position: absolute;
              left: 0;
              top: 0;
              color: white;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              font-size: 14px;
            }
            .step-number-1 {
              background: #3182CE !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .step-number-2 {
              background: #D69E2E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .step-number-3 {
              background: #E53E3E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .footer {
              margin-top: 60px;
              font-size: 16px;
              color: #718096;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <!-- Brand bar -->
            <div class="brand-bar">
              <div class="brand-bar-blue"></div>
              <div class="brand-bar-yellow"></div>
              <div class="brand-bar-red"></div>
            </div>
            
            <!-- Header -->
            <h1 class="title">Indie Points</h1>
            <p class="subtitle">Earn points, get rewards, support local businesses</p>
            
            <!-- Business name -->
            <h2 class="business-name">${businessProfile.businessName}</h2>
            
            <!-- QR Code -->
            <div class="qr-container">
              <div style="width: 300px; height: 300px; border: 2px solid #000; display: flex; align-items: center; justify-content: center; padding: 20px; box-sizing: border-box;">
                <img src="data:image/png;base64,${qrCodeBase64}" alt="Visit QR Code" style="max-width: 100%; max-height: 100%; object-fit: contain;" />
              </div>
            </div>
            
            <!-- Instructions -->
            <div class="instructions">
              <div class="instruction-step">
                <div class="step-number step-number-1">1</div>
                Download the Indie Points app from your app store
              </div>
              <div class="instruction-step">
                <div class="step-number step-number-2">2</div>
                Open the app and scan this QR code with your camera
              </div>
              <div class="instruction-step">
                <div class="step-number step-number-3">3</div>
                Earn your free visit point instantly!
              </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
              <p>Visit points can only be claimed once per business.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  };

  // Handle print
  const handlePrint = async () => {
    if (!businessProfile) {
      Alert.alert('Error', 'Business profile not found');
      return;
    }

    // Try to generate QR base64 if not available
    if (!qrCodeBase64 && qrRef.current) {
      qrRef.current.toDataURL((dataURL: string) => {
        setQrCodeBase64(dataURL);
        // Retry print after base64 is generated
        setTimeout(() => {
          handlePrint();
        }, 100);
      });
      return;
    }

    if (!qrCodeBase64) {
      Alert.alert(
        'Error',
        'QR code image not ready. Please wait a moment and try again.'
      );
      return;
    }

    setIsPrinting(true);
    try {
      const htmlContent = createPrintHTML();

      await Print.printAsync({
        html: htmlContent,
        width: 595, // A4 width in points
        height: 842, // A4 height in points
      });
    } catch (error) {
      console.error('Print error:', error);
      Alert.alert('Print Error', 'Failed to print. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        <HeaderSection />
        <Box className='px-6 pb-8'>
          {loading ? (
            <QRCodeLoadingState />
          ) : error ? (
            <QRCodeErrorState error={error} />
          ) : businessProfile ? (
            <VStack space='xl'>
              <BusinessQRCodeSection
                businessProfile={businessProfile}
                onPrint={handlePrint}
                isPrinting={isPrinting}
                onQRRef={handleQRRef}
              />
              <HowToUseSection />
            </VStack>
          ) : null}
        </Box>
      </ScrollView>
    </Box>
  );
}
