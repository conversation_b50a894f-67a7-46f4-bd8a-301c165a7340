import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView } from 'react-native';

import { CameraScanSection } from '../../components/(tabs)/scan/CameraScanSection';
import { ConfirmationSection } from '../../components/(tabs)/scan/ConfirmationSection';
import { HeaderSection } from '../../components/(tabs)/scan/HeaderSection';
import { ScanErrorState } from '../../components/(tabs)/scan/ScanErrorState';
import { ScanFormSection } from '../../components/(tabs)/scan/ScanFormSection';
import { ScanLoadingState } from '../../components/(tabs)/scan/ScanLoadingState';
import { SuccessSection } from '../../components/(tabs)/scan/SuccessSection';
import { useScanScreenData } from '../../hooks/composite/useScanScreenData';

export default function Scan() {
  const [permission, requestPermission] = useCameraPermissions();
  const [purchaseAmount, setPurchaseAmount] = useState('');
  const [showPurchaseForm, setShowPurchaseForm] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    businessProfile,
    scannedData,
    customerPoints,
    eligibleRewards,
    rewardToggles,
    step,
    transactionSummary,
    loading,
    processingTransaction,
    error,
    setScannedData,
    setRewardToggles,
    setStep,
    setTransactionSummary,
    parseCustomerQRCodeData,
    fetchCustomerPointsAndRewards,
    calculatePointsAwarded,
    calculatePointsRedeemed,
    resetScanState,
    processTransaction,
  } = useScanScreenData();

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Handle customer QR code scan
  const handleBarCodeScanned = useCallback(
    async (data: string) => {
      if (processingTransaction || !businessProfile) return;

      const qrData = parseCustomerQRCodeData(data);
      if (!qrData) {
        setErrorMessage(
          'Invalid or expired customer loyalty card. Please ask the customer to refresh their loyalty card.'
        );
        return;
      }

      setScannedData(qrData);
      setShowPurchaseForm(true);
      // Fetch eligible rewards for this customer
      await fetchCustomerPointsAndRewards(qrData.userId);
    },
    [
      processingTransaction,
      businessProfile,
      parseCustomerQRCodeData,
      setScannedData,
      fetchCustomerPointsAndRewards,
    ]
  );

  // Handle purchase form confirm (show summary)
  const handleShowConfirmation = useCallback(() => {
    if (!scannedData || !businessProfile) return;
    const amount = parseFloat(purchaseAmount);
    if (!purchaseAmount || isNaN(amount) || amount <= 0) {
      setErrorMessage('Please enter a valid purchase amount.');
      return;
    }
    const selectedRewardIds = Object.entries(rewardToggles)
      .filter(([_, isSelected]) => isSelected)
      .map(([rewardId]) => rewardId);
    if (selectedRewardIds.length > 0 && customerPoints !== null) {
      const totalPointsRequired = calculatePointsRedeemed(selectedRewardIds);
      if (totalPointsRequired > customerPoints) {
        setErrorMessage(
          `Selected rewards require ${totalPointsRequired} points, but customer only has ${customerPoints} points. Please deselect some rewards.`
        );
        return;
      }
    }
    const pointsAwarded = calculatePointsAwarded(amount);
    const pointsRedeemed = calculatePointsRedeemed(selectedRewardIds);
    const redeemedRewards = selectedRewardIds
      .map(rewardId => eligibleRewards.find(r => r.id === rewardId)?.title)
      .filter(Boolean) as string[];
    setTransactionSummary({
      amount,
      pointsAwarded,
      pointsRedeemed,
      redeemedRewards,
    });
    setStep('confirm');
  }, [
    scannedData,
    businessProfile,
    purchaseAmount,
    rewardToggles,
    customerPoints,
    calculatePointsRedeemed,
    calculatePointsAwarded,
    eligibleRewards,
    setTransactionSummary,
    setStep,
  ]);

  // Handle actual transaction after confirmation
  const handleCreatePurchase = useCallback(async () => {
    if (!transactionSummary) return;

    const selectedRewardIds = Object.entries(rewardToggles)
      .filter(([_, isSelected]) => isSelected)
      .map(([rewardId]) => rewardId);

    const result = await processTransaction(
      transactionSummary.amount,
      selectedRewardIds
    );
    if (result.data) {
      setStep('complete');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else if (result.error) {
      setErrorMessage(result.error);
    }
  }, [transactionSummary, rewardToggles, processTransaction, setStep]);

  // Update handleScanAgain to reset step and summary
  const handleScanAgain = useCallback(() => {
    resetScanState();
    setShowPurchaseForm(false);
    setPurchaseAmount('');
    setErrorMessage(null);
  }, [resetScanState]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        <HeaderSection />
        <Box className='px-6 pb-8'>
          {loading ? (
            <ScanLoadingState />
          ) : error ? (
            <ScanErrorState error={error} />
          ) : (
            <VStack space='xl'>
              {/* Camera Scan Section */}
              {!showPurchaseForm && (
                <CameraScanSection
                  hasPermission={permission?.granted || false}
                  onBarCodeScanned={handleBarCodeScanned}
                  onRequestPermission={requestPermission}
                />
              )}

              {/* Purchase Form */}
              {showPurchaseForm && scannedData && step === 'form' && (
                <ScanFormSection
                  customerPoints={customerPoints}
                  eligibleRewards={eligibleRewards}
                  rewardToggles={rewardToggles}
                  purchaseAmount={purchaseAmount}
                  errorMessage={errorMessage}
                  isProcessing={processingTransaction}
                  onRewardToggle={(rewardId, value) =>
                    setRewardToggles(toggles => ({
                      ...toggles,
                      [rewardId]: value,
                    }))
                  }
                  onPurchaseAmountChange={value => {
                    setPurchaseAmount(value);
                    if (
                      errorMessage === 'Please enter a valid purchase amount.'
                    ) {
                      setErrorMessage(null);
                    }
                  }}
                  onCancel={handleScanAgain}
                  onConfirm={handleShowConfirmation}
                />
              )}

              {/* Confirmation Section */}
              {showPurchaseForm &&
                scannedData &&
                step === 'confirm' &&
                transactionSummary && (
                  <ConfirmationSection
                    transactionSummary={transactionSummary}
                    isProcessing={processingTransaction}
                    onCancel={() => setStep('form')}
                    onConfirm={handleCreatePurchase}
                  />
                )}

              {/* Success Section */}
              {showPurchaseForm &&
                scannedData &&
                step === 'complete' &&
                transactionSummary && (
                  <SuccessSection
                    transactionSummary={transactionSummary}
                    onScanAgain={handleScanAgain}
                  />
                )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
