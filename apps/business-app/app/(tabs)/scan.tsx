import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import React, { useCallback, useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView } from 'react-native';

import { CameraScanSection } from '../../components/(tabs)/scan/CameraScanSection';
import { ConfirmationSection } from '../../components/(tabs)/scan/ConfirmationSection';
import { HeaderSection } from '../../components/(tabs)/scan/HeaderSection';
import { ScanErrorState } from '../../components/(tabs)/scan/ScanErrorState';
import { ScanFormSection } from '../../components/(tabs)/scan/ScanFormSection';
import { ScanLoadingState } from '../../components/(tabs)/scan/ScanLoadingState';
import { SuccessSection } from '../../components/(tabs)/scan/SuccessSection';
import { useScanScreenData } from '../../hooks/composite/useScanScreenData';

export default function Scan() {
  const [permission, requestPermission] = useCameraPermissions();
  const [purchaseAmount, setPurchaseAmount] = useState('');
  const [showPurchaseForm, setShowPurchaseForm] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    businessProfile,
    scannedData,
    customerPoints,
    eligibleRewards,
    rewardToggles,
    step,
    transactionSummary,
    loading,
    loadingRewards,
    loadingCustomers,
    processingTransaction,
    error,
    setScannedData,
    setRewardToggles,
    setStep,
    setTransactionSummary,
    parseCustomerQRCodeData,
    fetchCustomerPointsAndRewards,
    calculatePointsAwarded,
    calculatePointsRedeemed,
    resetScanState,
    processTransaction,
  } = useScanScreenData();

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Handle customer QR code scan
  const handleBarCodeScanned = useCallback(
    async (data: string) => {
      if (processingTransaction || !businessProfile) return;

      const qrData = parseCustomerQRCodeData(data);
      if (!qrData) {
        setErrorMessage(
          'Invalid or expired customer loyalty card. Please ask the customer to refresh their loyalty card.'
        );
        return;
      }

      setScannedData(qrData);
      setShowPurchaseForm(true);
      // Fetch eligible rewards for this customer
      await fetchCustomerPointsAndRewards(qrData.userId);
    },
    [
      processingTransaction,
      businessProfile,
      parseCustomerQRCodeData,
      setScannedData,
      fetchCustomerPointsAndRewards,
    ]
  );

  // Handle purchase form confirm (show summary)
  const handleShowConfirmation = useCallback(() => {
    if (!scannedData || !businessProfile) return;
    const amount = parseFloat(purchaseAmount);
    if (!purchaseAmount || isNaN(amount) || amount <= 0) {
      setErrorMessage('Please enter a valid purchase amount.');
      return;
    }
    const selectedRewardIds = Object.entries(rewardToggles)
      .filter(([_, isSelected]) => isSelected)
      .map(([rewardId]) => rewardId);
    if (selectedRewardIds.length > 0 && customerPoints !== null) {
      const totalPointsRequired = calculatePointsRedeemed(selectedRewardIds);
      if (totalPointsRequired > customerPoints) {
        setErrorMessage(
          `Selected rewards require ${totalPointsRequired} points, but customer only has ${customerPoints} points. Please deselect some rewards.`
        );
        return;
      }
    }
    const pointsAwarded = calculatePointsAwarded(amount);
    const pointsRedeemed = calculatePointsRedeemed(selectedRewardIds);
    const redeemedRewards = selectedRewardIds
      .map(rewardId => eligibleRewards.find(r => r.id === rewardId)?.title)
      .filter(Boolean) as string[];
    setTransactionSummary({
      amount,
      pointsAwarded,
      pointsRedeemed,
      redeemedRewards,
    });
    setStep('confirm');
  }, [
    scannedData,
    businessProfile,
    purchaseAmount,
    rewardToggles,
    customerPoints,
    calculatePointsRedeemed,
    calculatePointsAwarded,
    eligibleRewards,
    setTransactionSummary,
    setStep,
  ]);

  // Handle actual transaction after confirmation
  const handleCreatePurchase = useCallback(async () => {
    if (!transactionSummary) return;

    const selectedRewardIds = Object.entries(rewardToggles)
      .filter(([_, isSelected]) => isSelected)
      .map(([rewardId]) => rewardId);

    const result = await processTransaction(
      transactionSummary.amount,
      selectedRewardIds
    );
    if (result.data) {
      setStep('complete');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else if (result.error) {
      setErrorMessage(result.error);
    }
  }, [transactionSummary, rewardToggles, processTransaction, setStep]);

  // Update handleScanAgain to reset step and summary
  const handleScanAgain = useCallback(() => {
    resetScanState();
    setShowPurchaseForm(false);
    setPurchaseAmount('');
    setErrorMessage(null);
  }, [resetScanState]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        <HeaderSection />
        <Box className='px-6 pb-8'>
          {loading ? (
            <ScanLoadingState />
          ) : error ? (
            <ScanErrorState error={error} />
          ) : (
            <VStack space='xl'>
              {/* Camera Scan Section */}
              {!showPurchaseForm && (
                <CameraScanSection
                  hasPermission={permission?.granted || false}
                  onBarCodeScanned={handleBarCodeScanned}
                  onRequestPermission={requestPermission}
                />
              )}

              {/* Purchase Form */}
              {showPurchaseForm && scannedData && step === 'form' && (
                <ScanFormSection
                  customerPoints={customerPoints}
                  eligibleRewards={eligibleRewards}
                  rewardToggles={rewardToggles}
                  purchaseAmount={purchaseAmount}
                  errorMessage={errorMessage}
                  isProcessing={processingTransaction}
                  onRewardToggle={(rewardId, value) =>
                    setRewardToggles(toggles => ({
                      ...toggles,
                      [rewardId]: value,
                    }))
                  }
                  onPurchaseAmountChange={(value) => {
                    setPurchaseAmount(value);
                    if (errorMessage === 'Please enter a valid purchase amount.') {
                      setErrorMessage(null);
                    }
                  }}
                  onCancel={handleScanAgain}
                  onConfirm={handleShowConfirmation}
                />
              )}

              {/* Confirmation Section */}
              {showPurchaseForm && scannedData && step === 'confirm' && transactionSummary && (
                <ConfirmationSection
                  transactionSummary={transactionSummary}
                  isProcessing={processingTransaction}
                  onCancel={() => setStep('form')}
                  onConfirm={handleCreatePurchase}
                />
              )}

              {/* Success Section */}
              {showPurchaseForm && scannedData && step === 'complete' && transactionSummary && (
                <SuccessSection
                  transactionSummary={transactionSummary}
                  onScanAgain={handleScanAgain}
                />
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
                  <VStack space='lg'>
                    <Heading
                      size='lg'
                      className='text-typography-900 font-semibold text-center'
                    >
                      Process transaction
                    </Heading>
                    <Text size='md' className='text-typography-600 text-left'>
                      Loyalty card scanned successfully.
                    </Text>
                    {/* Show customer points */}
                    {customerPoints !== null && (
                      <Text size='md' className='text-typography-600 text-left'>
                        Customer has{' '}
                        <Text className='font-bold'>{customerPoints}</Text>{' '}
                        points.
                      </Text>
                    )}
                    {/* Eligible rewards UI */}
                    {rewardsLoading ? (
                      <VStack space='md' className='items-center'>
                        <Spinner size='small' />
                        <Text size='sm' className='text-typography-600'>
                          Checking for eligible rewards...
                        </Text>
                      </VStack>
                    ) : eligibleRewards.length > 0 ? (
                      <VStack space='md'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          Eligible Rewards
                        </Text>
                        {eligibleRewards.map(reward => (
                          <HStack
                            key={reward.id}
                            space='md'
                            className='items-center'
                          >
                            <Box className='flex-1'>
                              <Text
                                size='md'
                                className='text-typography-900 font-medium'
                              >
                                {reward.title}
                              </Text>
                              <Text size='sm' className='text-typography-600'>
                                {reward.pointsRequired} points required
                              </Text>
                            </Box>
                            <Switch
                              value={rewardToggles[reward.id] || false}
                              onValueChange={val =>
                                setRewardToggles(toggles => ({
                                  ...toggles,
                                  [reward.id]: val,
                                }))
                              }
                              accessibilityLabel={`Toggle to redeem ${reward.title}`}
                              trackColor={{ true: '#0284C7' }}
                            />
                          </HStack>
                        ))}
                      </VStack>
                    ) : (
                      <Text
                        size='sm'
                        className='text-typography-600 text-center'
                      >
                        No eligible rewards for this customer.
                      </Text>
                    )}
                    <VStack space='md'>
                      <Text
                        size='md'
                        className='text-typography-900 font-medium'
                      >
                        Transaction amount (£)
                      </Text>
                      {/* Validation error if purchaseAmount is empty and user tries to confirm */}
                      {errorMessage ===
                        'Please enter a valid purchase amount.' && (
                        <Text size='sm' className='text-error-500'>
                          Please enter a valid transaction amount.
                        </Text>
                      )}
                      {/* Show info if any rewards are being redeemed */}
                      {Object.values(rewardToggles).some(Boolean) && (
                        <Text size='sm' className='text-typography-600'>
                          Enter the final transaction amount after rewards have
                          been redeemed.
                        </Text>
                      )}
                      <Input>
                        <InputField
                          placeholder='0.00'
                          value={purchaseAmount}
                          onChangeText={text => {
                            setPurchaseAmount(text);
                            if (
                              errorMessage ===
                              'Please enter a valid purchase amount.'
                            ) {
                              setErrorMessage(null);
                            }
                          }}
                          keyboardType='decimal-pad'
                          autoFocus={false}
                        />
                      </Input>
                    </VStack>
                    <HStack space='md'>
                      <Button
                        size='lg'
                        action='secondary'
                        className='flex-1'
                        onPress={handleScanAgain}
                      >
                        <ButtonText>Cancel</ButtonText>
                      </Button>
                      <Button
                        size='lg'
                        action='primary'
                        className='flex-1'
                        onPress={handleShowConfirmation}
                        disabled={isProcessing}
                      >
                        <ButtonText>Confirm</ButtonText>
                      </Button>
                    </HStack>
                  </VStack>
                </Box>
              )}

              {/* Confirmation Receipt Step */}
              {showPurchaseForm &&
                scannedData &&
                step === 'confirm' &&
                transactionSummary && (
                  <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                    <VStack space='lg' className='items-center'>
                      <Heading
                        size='lg'
                        className='text-typography-900 font-semibold text-center'
                      >
                        Confirm transaction
                      </Heading>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <VStack space='sm' className='w-full'>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Amount:</Text> £
                          {transactionSummary.amount.toFixed(2)}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points awarded:</Text>{' '}
                          {transactionSummary.pointsAwarded}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points redeemed:</Text>{' '}
                          {transactionSummary.pointsRedeemed}
                        </Text>
                        {transactionSummary.redeemedRewards.length > 0 && (
                          <Text size='md' className='text-typography-900'>
                            <Text className='font-bold'>Rewards redeemed:</Text>{' '}
                            {transactionSummary.redeemedRewards.join(', ')}
                          </Text>
                        )}
                      </VStack>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <HStack space='md'>
                        <Button
                          size='lg'
                          action='secondary'
                          className='flex-1'
                          onPress={() => setStep('form')}
                          disabled={isProcessing}
                        >
                          <ButtonText>Cancel</ButtonText>
                        </Button>
                        <Button
                          size='lg'
                          action='primary'
                          className='flex-1'
                          onPress={handleCreatePurchase}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <Spinner size='small' color='white' />
                          ) : (
                            <ButtonText>Confirm</ButtonText>
                          )}
                        </Button>
                      </HStack>
                    </VStack>
                  </Box>
                )}

              {/* Transaction Complete Step */}
              {showPurchaseForm &&
                scannedData &&
                step === 'complete' &&
                transactionSummary && (
                  <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                    <VStack space='lg' className='items-center'>
                      <FontAwesome
                        name='check-circle'
                        size={64}
                        color='#0284c7'
                      />
                      <Heading
                        size='lg'
                        className='text-typography-900 font-semibold text-center'
                      >
                        Transaction complete
                      </Heading>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <VStack space='sm' className='w-full'>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Amount:</Text> £
                          {transactionSummary.amount.toFixed(2)}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points awarded:</Text>{' '}
                          {transactionSummary.pointsAwarded}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points redeemed:</Text>{' '}
                          {transactionSummary.pointsRedeemed}
                        </Text>
                        {transactionSummary.redeemedRewards.length > 0 && (
                          <Text size='md' className='text-typography-900'>
                            <Text className='font-bold'>Rewards redeemed:</Text>{' '}
                            {transactionSummary.redeemedRewards.join(', ')}
                          </Text>
                        )}
                      </VStack>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <Button
                        size='lg'
                        className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
                        onPress={handleScanAgain}
                      >
                        <HStack space='sm' className='items-center'>
                          <FontAwesome name='camera' size={16} color='white' />
                          <ButtonText className='text-white font-semibold'>
                            Scan again
                          </ButtonText>
                        </HStack>
                      </Button>
                    </VStack>
                  </Box>
                )}

              {/* Camera section */}
              {!showPurchaseForm && (
                <VStack space='lg' className='items-center'>
                  <Heading
                    size='xl'
                    className='text-typography-900 font-semibold'
                  >
                    Scan customer loyalty card
                  </Heading>
                  <Text size='md' className='text-typography-600 text-center'>
                    Ask the customer to show their loyalty card from the Indie
                    Points app
                  </Text>
                  <Box className='w-full aspect-square bg-black rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg overflow-hidden'>
                    {!permission ? (
                      <Text size='md' className='text-white'>
                        Requesting camera permission...
                      </Text>
                    ) : !permission.granted ? (
                      <VStack space='md' className='items-center'>
                        <FontAwesome name='camera' size={64} color='#fff' />
                        <Text size='md' className='text-white text-center'>
                          Camera access is required to scan customer loyalty
                          cards.
                        </Text>
                      </VStack>
                    ) : scanCompleted ? (
                      <VStack
                        space='lg'
                        className='items-center justify-center flex-1'
                      >
                        <FontAwesome
                          name='check-circle'
                          size={64}
                          color='#22c55e'
                        />
                        <Text
                          size='lg'
                          className='text-white text-center font-semibold'
                        >
                          Customer loyalty card scanned!
                        </Text>
                        <Button
                          size='lg'
                          action='primary'
                          className='mt-4 bg-primary-500'
                          onPress={handleScanAgain}
                        >
                          <ButtonText>Scan another</ButtonText>
                        </Button>
                      </VStack>
                    ) : (
                      <CameraView
                        style={{ width: '100%', aspectRatio: 1 }}
                        facing={facing}
                        ratio='1:1'
                        barcodeScannerSettings={{
                          barcodeTypes: ['qr'],
                        }}
                        onBarcodeScanned={handleBarCodeScanned}
                      />
                    )}
                  </Box>
                </VStack>
              )}

              {/* How to Process Purchases Section */}
              <VStack space='lg'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  How to process transactions
                </Heading>

                {/* Step 1 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      1
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Ask the customer for their loyalty card
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      The customer opens the Indie Points app and shows their
                      loyalty card.
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 2 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      2
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Scan and enter transaction amount
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Use the camera to scan the customer&apos;s loyalty card
                      and enter the final transaction amount after any rewards
                      have been redeemed.
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 3 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      3
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Complete the transaction
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Points are automatically awarded and any selected rewards
                      will be redeemed from the customer&apos;s points balance.
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
