import { Grad<PERSON><PERSON><PERSON> } from '@indie-points/auth';
import { useAuth } from '@indie-points/contexts';
import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import { getTimeBasedGreeting } from '@indie-points/utils';
import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView } from 'react-native';

import { BusinessPointsSummary, BusinessService , PointsService } from '../../services';

export default function Home() {
  const { user } = useAuth();
  const [pointsData, setPointsData] = useState<BusinessPointsSummary | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch business data when component mounts or user changes
  const fetchBusinessData = useCallback(async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // First get the business profile
      const profileResult = await BusinessService.getBusinessProfile(user.id);

      if (profileResult.error) {
        setError(profileResult.error);
        setLoading(false);
        return;
      }

      if (!profileResult.data) {
        setError(
          'No business profile found. Please complete your business setup in Settings.'
        );
        setLoading(false);
        return;
      }

      // Then get business stats using the business profile ID
      const pointsResult = await PointsService.getBusinessPointsSummary(
        profileResult.data.id
      );

      if (pointsResult.error) {
        setError(pointsResult.error);
      } else {
        setPointsData(pointsResult.data);
      }
    } catch (error) {
      console.error('Error fetching business data:', error);
      setError('Failed to load business data');
    }

    setLoading(false);
  }, [user?.id]);

  useEffect(() => {
    fetchBusinessData();
  }, [fetchBusinessData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchBusinessData(),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Home
          </Heading>

          {/* Colored divider line */}
          <GradientBar />

          {/* Greeting */}
          <VStack space='xs' className='items-center'>
            <Heading
              size='xl'
              className='text-typography-900 font-semibold text-center'
            >
              {getTimeBasedGreeting()}.
            </Heading>
            <Heading
              size='xl'
              className='text-typography-900 font-semibold text-center'
            >
              Welcome to Indie Points!
            </Heading>
            <Text size='md' className='text-typography-600 text-center'>
              Here is your business points summary.
            </Text>
          </VStack>
        </VStack>

        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading your points...
              </Text>
            </VStack>
          ) : error ? (
            // Error state
            <VStack space='lg' className='items-center py-8'>
              <Text
                size='lg'
                className='text-error-500 font-semibold text-center'
              >
                Unable to load points data
              </Text>
              <Text size='md' className='text-typography-600 text-center'>
                {error}
              </Text>
              <Text size='sm' className='text-typography-500 text-center'>
                Please try again later or contact support if the problem
                persists.
              </Text>
            </VStack>
          ) : (
            // Data loaded successfully
            <VStack space='lg'>
              {/* Active Points Card */}
              <Box className='bg-primary-500 rounded-2xl border-4 border-primary-700 shadow-lg p-6'>
                <VStack space='xs'>
                  <Text size='lg' className='text-white font-medium'>
                    Active points
                  </Text>
                  <Heading size='4xl' className='text-white font-bold'>
                    {pointsData?.totalActive?.toLocaleString() || '0'}
                  </Heading>
                </VStack>
              </Box>

              {/* Points Awarded and Redeemed Cards */}
              <HStack space='md'>
                {/* Points Awarded Card */}
                <Box className='flex-1 bg-secondary-500 rounded-2xl border-4 border-secondary-700 shadow-lg p-4'>
                  <VStack space='xs'>
                    <Text size='md' className='text-white font-medium'>
                      Points awarded
                    </Text>
                    <Heading size='2xl' className='text-white font-bold'>
                      {pointsData?.totalEarned?.toLocaleString() || '0'}
                    </Heading>
                  </VStack>
                </Box>

                {/* Points Redeemed Card */}
                <Box className='flex-1 bg-error-500 rounded-2xl border-4 border-error-700 shadow-lg p-4'>
                  <VStack space='xs'>
                    <Text size='md' className='text-white font-medium'>
                      Points redeemed
                    </Text>
                    <Heading size='2xl' className='text-white font-bold'>
                      {pointsData?.totalRedeemed?.toLocaleString() || '0'}
                    </Heading>
                  </VStack>
                </Box>
              </HStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
